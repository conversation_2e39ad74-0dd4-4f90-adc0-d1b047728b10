.class public final enum Landroidx/compose/ui/autofill/AutofillType;
.super Ljava/lang/Enum;
.source "AutofillType.kt"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Enum<",
        "Landroidx/compose/ui/autofill/AutofillType;",
        ">;"
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u000c\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\u0008&\u0008\u0087\u0001\u0018\u00002\u0008\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\u0008\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\u0008\u0003j\u0002\u0008\u0004j\u0002\u0008\u0005j\u0002\u0008\u0006j\u0002\u0008\u0007j\u0002\u0008\u0008j\u0002\u0008\tj\u0002\u0008\nj\u0002\u0008\u000bj\u0002\u0008\u000cj\u0002\u0008\rj\u0002\u0008\u000ej\u0002\u0008\u000fj\u0002\u0008\u0010j\u0002\u0008\u0011j\u0002\u0008\u0012j\u0002\u0008\u0013j\u0002\u0008\u0014j\u0002\u0008\u0015j\u0002\u0008\u0016j\u0002\u0008\u0017j\u0002\u0008\u0018j\u0002\u0008\u0019j\u0002\u0008\u001aj\u0002\u0008\u001bj\u0002\u0008\u001cj\u0002\u0008\u001dj\u0002\u0008\u001ej\u0002\u0008\u001fj\u0002\u0008 j\u0002\u0008!j\u0002\u0008\"j\u0002\u0008#j\u0002\u0008$j\u0002\u0008%j\u0002\u0008&\u00a8\u0006\'"
    }
    d2 = {
        "Landroidx/compose/ui/autofill/AutofillType;",
        "",
        "(Ljava/lang/String;I)V",
        "EmailAddress",
        "Username",
        "Password",
        "NewUsername",
        "NewPassword",
        "PostalAddress",
        "PostalCode",
        "CreditCardNumber",
        "CreditCardSecurityCode",
        "CreditCardExpirationDate",
        "CreditCardExpirationMonth",
        "CreditCardExpirationYear",
        "CreditCardExpirationDay",
        "AddressCountry",
        "AddressRegion",
        "AddressLocality",
        "AddressStreet",
        "AddressAuxiliaryDetails",
        "PostalCodeExtended",
        "PersonFullName",
        "PersonFirstName",
        "PersonLastName",
        "PersonMiddleName",
        "PersonMiddleInitial",
        "PersonNamePrefix",
        "PersonNameSuffix",
        "PhoneNumber",
        "PhoneNumberDevice",
        "PhoneCountryCode",
        "PhoneNumberNational",
        "Gender",
        "BirthDateFull",
        "BirthDateDay",
        "BirthDateMonth",
        "BirthDateYear",
        "SmsOtpCode",
        "ui_release"
    }
    k = 0x1
    mv = {
        0x1,
        0x8,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field private static final synthetic $VALUES:[Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum AddressAuxiliaryDetails:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum AddressCountry:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum AddressLocality:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum AddressRegion:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum AddressStreet:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum BirthDateDay:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum BirthDateFull:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum BirthDateMonth:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum BirthDateYear:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum CreditCardExpirationDate:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum CreditCardExpirationDay:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum CreditCardExpirationMonth:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum CreditCardExpirationYear:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum CreditCardNumber:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum CreditCardSecurityCode:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum EmailAddress:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum Gender:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum NewPassword:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum NewUsername:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum Password:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum PersonFirstName:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum PersonFullName:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum PersonLastName:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum PersonMiddleInitial:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum PersonMiddleName:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum PersonNamePrefix:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum PersonNameSuffix:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum PhoneCountryCode:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum PhoneNumber:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum PhoneNumberDevice:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum PhoneNumberNational:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum PostalAddress:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum PostalCode:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum PostalCodeExtended:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum SmsOtpCode:Landroidx/compose/ui/autofill/AutofillType;

.field public static final enum Username:Landroidx/compose/ui/autofill/AutofillType;


# direct methods
.method private static final synthetic $values()[Landroidx/compose/ui/autofill/AutofillType;
    .locals 36

    sget-object v0, Landroidx/compose/ui/autofill/AutofillType;->EmailAddress:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v1, Landroidx/compose/ui/autofill/AutofillType;->Username:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v2, Landroidx/compose/ui/autofill/AutofillType;->Password:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v3, Landroidx/compose/ui/autofill/AutofillType;->NewUsername:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v4, Landroidx/compose/ui/autofill/AutofillType;->NewPassword:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v5, Landroidx/compose/ui/autofill/AutofillType;->PostalAddress:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v6, Landroidx/compose/ui/autofill/AutofillType;->PostalCode:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v7, Landroidx/compose/ui/autofill/AutofillType;->CreditCardNumber:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v8, Landroidx/compose/ui/autofill/AutofillType;->CreditCardSecurityCode:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v9, Landroidx/compose/ui/autofill/AutofillType;->CreditCardExpirationDate:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v10, Landroidx/compose/ui/autofill/AutofillType;->CreditCardExpirationMonth:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v11, Landroidx/compose/ui/autofill/AutofillType;->CreditCardExpirationYear:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v12, Landroidx/compose/ui/autofill/AutofillType;->CreditCardExpirationDay:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v13, Landroidx/compose/ui/autofill/AutofillType;->AddressCountry:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v14, Landroidx/compose/ui/autofill/AutofillType;->AddressRegion:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v15, Landroidx/compose/ui/autofill/AutofillType;->AddressLocality:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v16, Landroidx/compose/ui/autofill/AutofillType;->AddressStreet:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v17, Landroidx/compose/ui/autofill/AutofillType;->AddressAuxiliaryDetails:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v18, Landroidx/compose/ui/autofill/AutofillType;->PostalCodeExtended:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v19, Landroidx/compose/ui/autofill/AutofillType;->PersonFullName:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v20, Landroidx/compose/ui/autofill/AutofillType;->PersonFirstName:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v21, Landroidx/compose/ui/autofill/AutofillType;->PersonLastName:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v22, Landroidx/compose/ui/autofill/AutofillType;->PersonMiddleName:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v23, Landroidx/compose/ui/autofill/AutofillType;->PersonMiddleInitial:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v24, Landroidx/compose/ui/autofill/AutofillType;->PersonNamePrefix:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v25, Landroidx/compose/ui/autofill/AutofillType;->PersonNameSuffix:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v26, Landroidx/compose/ui/autofill/AutofillType;->PhoneNumber:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v27, Landroidx/compose/ui/autofill/AutofillType;->PhoneNumberDevice:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v28, Landroidx/compose/ui/autofill/AutofillType;->PhoneCountryCode:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v29, Landroidx/compose/ui/autofill/AutofillType;->PhoneNumberNational:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v30, Landroidx/compose/ui/autofill/AutofillType;->Gender:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v31, Landroidx/compose/ui/autofill/AutofillType;->BirthDateFull:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v32, Landroidx/compose/ui/autofill/AutofillType;->BirthDateDay:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v33, Landroidx/compose/ui/autofill/AutofillType;->BirthDateMonth:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v34, Landroidx/compose/ui/autofill/AutofillType;->BirthDateYear:Landroidx/compose/ui/autofill/AutofillType;

    sget-object v35, Landroidx/compose/ui/autofill/AutofillType;->SmsOtpCode:Landroidx/compose/ui/autofill/AutofillType;

    filled-new-array/range {v0 .. v35}, [Landroidx/compose/ui/autofill/AutofillType;

    move-result-object v0

    return-object v0
.end method

.method static constructor <clinit>()V
    .locals 3

    .line 34
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "EmailAddress"

    const/4 v2, 0x0

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->EmailAddress:Landroidx/compose/ui/autofill/AutofillType;

    .line 39
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "Username"

    const/4 v2, 0x1

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->Username:Landroidx/compose/ui/autofill/AutofillType;

    .line 44
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "Password"

    const/4 v2, 0x2

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->Password:Landroidx/compose/ui/autofill/AutofillType;

    .line 50
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "NewUsername"

    const/4 v2, 0x3

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->NewUsername:Landroidx/compose/ui/autofill/AutofillType;

    .line 56
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "NewPassword"

    const/4 v2, 0x4

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->NewPassword:Landroidx/compose/ui/autofill/AutofillType;

    .line 61
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "PostalAddress"

    const/4 v2, 0x5

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->PostalAddress:Landroidx/compose/ui/autofill/AutofillType;

    .line 66
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "PostalCode"

    const/4 v2, 0x6

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->PostalCode:Landroidx/compose/ui/autofill/AutofillType;

    .line 71
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "CreditCardNumber"

    const/4 v2, 0x7

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->CreditCardNumber:Landroidx/compose/ui/autofill/AutofillType;

    .line 76
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "CreditCardSecurityCode"

    const/16 v2, 0x8

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->CreditCardSecurityCode:Landroidx/compose/ui/autofill/AutofillType;

    .line 81
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "CreditCardExpirationDate"

    const/16 v2, 0x9

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->CreditCardExpirationDate:Landroidx/compose/ui/autofill/AutofillType;

    .line 87
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "CreditCardExpirationMonth"

    const/16 v2, 0xa

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->CreditCardExpirationMonth:Landroidx/compose/ui/autofill/AutofillType;

    .line 93
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "CreditCardExpirationYear"

    const/16 v2, 0xb

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->CreditCardExpirationYear:Landroidx/compose/ui/autofill/AutofillType;

    .line 98
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "CreditCardExpirationDay"

    const/16 v2, 0xc

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->CreditCardExpirationDay:Landroidx/compose/ui/autofill/AutofillType;

    .line 103
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "AddressCountry"

    const/16 v2, 0xd

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->AddressCountry:Landroidx/compose/ui/autofill/AutofillType;

    .line 108
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "AddressRegion"

    const/16 v2, 0xe

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->AddressRegion:Landroidx/compose/ui/autofill/AutofillType;

    .line 114
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "AddressLocality"

    const/16 v2, 0xf

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->AddressLocality:Landroidx/compose/ui/autofill/AutofillType;

    .line 119
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "AddressStreet"

    const/16 v2, 0x10

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->AddressStreet:Landroidx/compose/ui/autofill/AutofillType;

    .line 124
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "AddressAuxiliaryDetails"

    const/16 v2, 0x11

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->AddressAuxiliaryDetails:Landroidx/compose/ui/autofill/AutofillType;

    .line 132
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "PostalCodeExtended"

    const/16 v2, 0x12

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->PostalCodeExtended:Landroidx/compose/ui/autofill/AutofillType;

    .line 138
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "PersonFullName"

    const/16 v2, 0x13

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->PersonFullName:Landroidx/compose/ui/autofill/AutofillType;

    .line 143
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "PersonFirstName"

    const/16 v2, 0x14

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->PersonFirstName:Landroidx/compose/ui/autofill/AutofillType;

    .line 148
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "PersonLastName"

    const/16 v2, 0x15

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->PersonLastName:Landroidx/compose/ui/autofill/AutofillType;

    .line 153
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "PersonMiddleName"

    const/16 v2, 0x16

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->PersonMiddleName:Landroidx/compose/ui/autofill/AutofillType;

    .line 158
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "PersonMiddleInitial"

    const/16 v2, 0x17

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->PersonMiddleInitial:Landroidx/compose/ui/autofill/AutofillType;

    .line 163
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "PersonNamePrefix"

    const/16 v2, 0x18

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->PersonNamePrefix:Landroidx/compose/ui/autofill/AutofillType;

    .line 168
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "PersonNameSuffix"

    const/16 v2, 0x19

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->PersonNameSuffix:Landroidx/compose/ui/autofill/AutofillType;

    .line 176
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "PhoneNumber"

    const/16 v2, 0x1a

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->PhoneNumber:Landroidx/compose/ui/autofill/AutofillType;

    .line 182
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "PhoneNumberDevice"

    const/16 v2, 0x1b

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->PhoneNumberDevice:Landroidx/compose/ui/autofill/AutofillType;

    .line 187
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "PhoneCountryCode"

    const/16 v2, 0x1c

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->PhoneCountryCode:Landroidx/compose/ui/autofill/AutofillType;

    .line 193
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "PhoneNumberNational"

    const/16 v2, 0x1d

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->PhoneNumberNational:Landroidx/compose/ui/autofill/AutofillType;

    .line 198
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "Gender"

    const/16 v2, 0x1e

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->Gender:Landroidx/compose/ui/autofill/AutofillType;

    .line 203
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "BirthDateFull"

    const/16 v2, 0x1f

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->BirthDateFull:Landroidx/compose/ui/autofill/AutofillType;

    .line 208
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "BirthDateDay"

    const/16 v2, 0x20

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->BirthDateDay:Landroidx/compose/ui/autofill/AutofillType;

    .line 213
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "BirthDateMonth"

    const/16 v2, 0x21

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->BirthDateMonth:Landroidx/compose/ui/autofill/AutofillType;

    .line 218
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "BirthDateYear"

    const/16 v2, 0x22

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->BirthDateYear:Landroidx/compose/ui/autofill/AutofillType;

    .line 225
    new-instance v0, Landroidx/compose/ui/autofill/AutofillType;

    const-string v1, "SmsOtpCode"

    const/16 v2, 0x23

    invoke-direct {v0, v1, v2}, Landroidx/compose/ui/autofill/AutofillType;-><init>(Ljava/lang/String;I)V

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->SmsOtpCode:Landroidx/compose/ui/autofill/AutofillType;

    invoke-static {}, Landroidx/compose/ui/autofill/AutofillType;->$values()[Landroidx/compose/ui/autofill/AutofillType;

    move-result-object v0

    sput-object v0, Landroidx/compose/ui/autofill/AutofillType;->$VALUES:[Landroidx/compose/ui/autofill/AutofillType;

    return-void
.end method

.method private constructor <init>(Ljava/lang/String;I)V
    .locals 0
    .param p1, "$enum$name"    # Ljava/lang/String;
    .param p2, "$enum$ordinal"    # I
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    .line 29
    invoke-direct {p0, p1, p2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 30
    return-void
.end method

.method public static valueOf(Ljava/lang/String;)Landroidx/compose/ui/autofill/AutofillType;
    .locals 1

    const-class v0, Landroidx/compose/ui/autofill/AutofillType;

    invoke-static {v0, p0}, Ljava/lang/Enum;->valueOf(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;

    move-result-object v0

    check-cast v0, Landroidx/compose/ui/autofill/AutofillType;

    return-object v0
.end method

.method public static values()[Landroidx/compose/ui/autofill/AutofillType;
    .locals 1

    sget-object v0, Landroidx/compose/ui/autofill/AutofillType;->$VALUES:[Landroidx/compose/ui/autofill/AutofillType;

    invoke-virtual {v0}, [Ljava/lang/Object;->clone()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, [Landroidx/compose/ui/autofill/AutofillType;

    return-object v0
.end method
