.class public final Landroidx/compose/ui/focus/OneDimensionalFocusSearchKt;
.super Ljava/lang/Object;
.source "OneDimensionalFocusSearch.kt"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroidx/compose/ui/focus/OneDimensionalFocusSearchKt$WhenMappings;
    }
.end annotation

.annotation system Ldalvik/annotation/SourceDebugExtension;
    value = "SMAP\nOneDimensionalFocusSearch.kt\nKotlin\n*S Kotlin\n*F\n+ 1 OneDimensionalFocusSearch.kt\nandroidx/compose/ui/focus/OneDimensionalFocusSearchKt\n+ 2 MutableVector.kt\nandroidx/compose/runtime/collection/MutableVectorKt\n+ 3 NodeKind.kt\nandroidx/compose/ui/node/Nodes\n+ 4 DelegatableNode.kt\nandroidx/compose/ui/node/DelegatableNodeKt\n+ 5 fake.kt\nkotlin/jvm/internal/FakeKt\n+ 6 MutableVector.kt\nandroidx/compose/runtime/collection/MutableVector\n+ 7 Modifier.kt\nandroidx/compose/ui/Modifier$Node\n+ 8 DelegatingNode.kt\nandroidx/compose/ui/node/DelegatingNode\n*L\n1#1,266:1\n181#1,3:340\n184#1,2:344\n187#1,5:347\n196#1,3:352\n199#1,2:356\n202#1,5:359\n1187#2,2:267\n1208#2:274\n1187#2,2:275\n1187#2,2:364\n1208#2:371\n1187#2,2:372\n1187#2,2:448\n1208#2:455\n1187#2,2:456\n1208#2:570\n1187#2,2:571\n90#3:269\n90#3:366\n90#3:450\n90#3:532\n276#4:270\n133#4:271\n134#4:273\n135#4,7:277\n142#4,9:285\n385#4,6:294\n395#4,2:301\n397#4,17:306\n414#4,8:326\n151#4,6:334\n276#4:367\n133#4:368\n134#4:370\n135#4,7:374\n142#4,9:382\n385#4,6:391\n395#4,2:398\n397#4,17:403\n414#4,8:423\n151#4,6:431\n276#4:451\n133#4:452\n134#4:454\n135#4,7:458\n142#4,9:466\n385#4,6:475\n395#4,2:482\n397#4,17:487\n414#4,8:507\n151#4,6:515\n262#4:533\n230#4,5:534\n58#4:539\n59#4,8:541\n385#4,5:549\n263#4:554\n390#4:555\n395#4,2:557\n397#4,8:562\n405#4,9:573\n414#4,8:585\n68#4,7:593\n265#4:600\n1#5:272\n1#5:369\n1#5:453\n1#5:540\n48#6:284\n53#6:343\n523#6:346\n53#6:355\n523#6:358\n48#6:381\n204#6,11:437\n48#6:465\n492#6,11:521\n53#6:601\n523#6:602\n523#6:603\n53#6:604\n523#6:605\n523#6:606\n261#7:300\n261#7:397\n261#7:481\n261#7:556\n234#8,3:303\n237#8,3:323\n234#8,3:400\n237#8,3:420\n234#8,3:484\n237#8,3:504\n234#8,3:559\n237#8,3:582\n*S KotlinDebug\n*F\n+ 1 OneDimensionalFocusSearch.kt\nandroidx/compose/ui/focus/OneDimensionalFocusSearchKt\n*L\n133#1:340,3\n133#1:344,2\n133#1:347,5\n136#1:352,3\n136#1:356,2\n136#1:359,5\n128#1:267,2\n129#1:274\n129#1:275,2\n154#1:364,2\n155#1:371\n155#1:372,2\n164#1:448,2\n165#1:455\n165#1:456,2\n176#1:570\n176#1:571,2\n129#1:269\n155#1:366\n165#1:450\n176#1:532\n129#1:270\n129#1:271\n129#1:273\n129#1:277,7\n129#1:285,9\n129#1:294,6\n129#1:301,2\n129#1:306,17\n129#1:326,8\n129#1:334,6\n155#1:367\n155#1:368\n155#1:370\n155#1:374,7\n155#1:382,9\n155#1:391,6\n155#1:398,2\n155#1:403,17\n155#1:423,8\n155#1:431,6\n165#1:451\n165#1:452\n165#1:454\n165#1:458,7\n165#1:466,9\n165#1:475,6\n165#1:482,2\n165#1:487,17\n165#1:507,8\n165#1:515,6\n176#1:533\n176#1:534,5\n176#1:539\n176#1:541,8\n176#1:549,5\n176#1:554\n176#1:555\n176#1:557,2\n176#1:562,8\n176#1:573,9\n176#1:585,8\n176#1:593,7\n176#1:600\n129#1:272\n155#1:369\n165#1:453\n176#1:540\n129#1:284\n133#1:343\n133#1:346\n136#1:355\n136#1:358\n155#1:381\n158#1:437,11\n165#1:465\n168#1:521,11\n183#1:601\n185#1:602\n187#1:603\n198#1:604\n200#1:605\n202#1:606\n129#1:300\n155#1:397\n165#1:481\n176#1:556\n129#1:303,3\n129#1:323,3\n155#1:400,3\n155#1:420,3\n165#1:484,3\n165#1:504,3\n176#1:559,3\n176#1:582,3\n*E\n"
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u00000\n\u0000\n\u0002\u0010\u000e\n\u0002\u0008\u0005\n\u0002\u0010\u000b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0008\n\u0002\u0018\u0002\n\u0002\u0008\u000b\u001a \u0010\u0006\u001a\u00020\u0007*\u00020\u00082\u0012\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\u00070\nH\u0002\u001aE\u0010\u000b\u001a\u00020\u000c\"\u0004\u0008\u0000\u0010\r*\u0008\u0012\u0004\u0012\u0002H\r0\u000e2\u0006\u0010\u000f\u001a\u0002H\r2\u0012\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u0002H\r\u0012\u0004\u0012\u00020\u000c0\nH\u0082\u0008\u0082\u0002\u0008\n\u0006\u0008\u0001\u0012\u0002\u0010\u0002\u00a2\u0006\u0002\u0010\u0011\u001aE\u0010\u0012\u001a\u00020\u000c\"\u0004\u0008\u0000\u0010\r*\u0008\u0012\u0004\u0012\u0002H\r0\u000e2\u0006\u0010\u000f\u001a\u0002H\r2\u0012\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u0002H\r\u0012\u0004\u0012\u00020\u000c0\nH\u0082\u0008\u0082\u0002\u0008\n\u0006\u0008\u0001\u0012\u0002\u0010\u0002\u00a2\u0006\u0002\u0010\u0011\u001a \u0010\u0013\u001a\u00020\u0007*\u00020\u00082\u0012\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\u00070\nH\u0002\u001a:\u0010\u0014\u001a\u00020\u0007*\u00020\u00082\u0006\u0010\u0015\u001a\u00020\u00082\u0006\u0010\u0016\u001a\u00020\u00172\u0012\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\u00070\nH\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\u0008\u0018\u0010\u0019\u001a\u000c\u0010\u001a\u001a\u00020\u0007*\u00020\u0008H\u0002\u001a2\u0010\u001b\u001a\u00020\u0007*\u00020\u00082\u0006\u0010\u0016\u001a\u00020\u00172\u0012\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\u00070\nH\u0000\u00f8\u0001\u0000\u00a2\u0006\u0004\u0008\u001c\u0010\u001d\u001a \u0010\u001e\u001a\u00020\u0007*\u00020\u00082\u0012\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\u00070\nH\u0002\u001a \u0010\u001f\u001a\u00020\u0007*\u00020\u00082\u0012\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\u00070\nH\u0002\u001a:\u0010 \u001a\u00020\u0007*\u00020\u00082\u0006\u0010\u0015\u001a\u00020\u00082\u0006\u0010\u0016\u001a\u00020\u00172\u0012\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0008\u0012\u0004\u0012\u00020\u00070\nH\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\u0008!\u0010\u0019\"\u0014\u0010\u0000\u001a\u00020\u0001X\u0082T\u00a2\u0006\u0008\n\u0000\u0012\u0004\u0008\u0002\u0010\u0003\"\u0014\u0010\u0004\u001a\u00020\u0001X\u0082T\u00a2\u0006\u0008\n\u0000\u0012\u0004\u0008\u0005\u0010\u0003\u0082\u0002\u0007\n\u0005\u0008\u00a1\u001e0\u0001\u00a8\u0006\""
    }
    d2 = {
        "InvalidFocusDirection",
        "",
        "getInvalidFocusDirection$annotations",
        "()V",
        "NoActiveChild",
        "getNoActiveChild$annotations",
        "backwardFocusSearch",
        "",
        "Landroidx/compose/ui/focus/FocusTargetNode;",
        "onFound",
        "Lkotlin/Function1;",
        "forEachItemAfter",
        "",
        "T",
        "Landroidx/compose/runtime/collection/MutableVector;",
        "item",
        "action",
        "(Landroidx/compose/runtime/collection/MutableVector;Ljava/lang/Object;Lkotlin/jvm/functions/Function1;)V",
        "forEachItemBefore",
        "forwardFocusSearch",
        "generateAndSearchChildren",
        "focusedItem",
        "direction",
        "Landroidx/compose/ui/focus/FocusDirection;",
        "generateAndSearchChildren-4C6V_qg",
        "(Landroidx/compose/ui/focus/FocusTargetNode;Landroidx/compose/ui/focus/FocusTargetNode;ILkotlin/jvm/functions/Function1;)Z",
        "isRoot",
        "oneDimensionalFocusSearch",
        "oneDimensionalFocusSearch--OM-vw8",
        "(Landroidx/compose/ui/focus/FocusTargetNode;ILkotlin/jvm/functions/Function1;)Z",
        "pickChildForBackwardSearch",
        "pickChildForForwardSearch",
        "searchChildren",
        "searchChildren-4C6V_qg",
        "ui_release"
    }
    k = 0x2
    mv = {
        0x1,
        0x8,
        0x0
    }
    xi = 0x30
.end annotation


# static fields
.field private static final InvalidFocusDirection:Ljava/lang/String; = "This function should only be used for 1-D focus search"

.field private static final NoActiveChild:Ljava/lang/String; = "ActiveParent must have a focusedChild"


# direct methods
.method public static final synthetic access$searchChildren-4C6V_qg(Landroidx/compose/ui/focus/FocusTargetNode;Landroidx/compose/ui/focus/FocusTargetNode;ILkotlin/jvm/functions/Function1;)Z
    .locals 1
    .param p0, "$receiver"    # Landroidx/compose/ui/focus/FocusTargetNode;
    .param p1, "focusedItem"    # Landroidx/compose/ui/focus/FocusTargetNode;
    .param p2, "direction"    # I
    .param p3, "onFound"    # Lkotlin/jvm/functions/Function1;

    .line 1
    invoke-static {p0, p1, p2, p3}, Landroidx/compose/ui/focus/OneDimensionalFocusSearchKt;->searchChildren-4C6V_qg(Landroidx/compose/ui/focus/FocusTargetNode;Landroidx/compose/ui/focus/FocusTargetNode;ILkotlin/jvm/functions/Function1;)Z

    move-result v0

    return v0
.end method

.method private static final backwardFocusSearch(Landroidx/compose/ui/focus/FocusTargetNode;Lkotlin/jvm/functions/Function1;)Z
    .locals 6
    .param p0, "$this$backwardFocusSearch"    # Landroidx/compose/ui/focus/FocusTargetNode;
    .param p1, "onFound"    # Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/compose/ui/focus/FocusTargetNode;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroidx/compose/ui/focus/FocusTargetNode;",
            "Ljava/lang/Boolean;",
            ">;)Z"
        }
    .end annotation

    .line 67
    invoke-virtual {p0}, Landroidx/compose/ui/focus/FocusTargetNode;->getFocusState()Landroidx/compose/ui/focus/FocusStateImpl;

    move-result-object v0

    sget-object v1, Landroidx/compose/ui/focus/OneDimensionalFocusSearchKt$WhenMappings;->$EnumSwitchMapping$0:[I

    invoke-virtual {v0}, Landroidx/compose/ui/focus/FocusStateImpl;->ordinal()I

    move-result v0

    aget v0, v1, v0

    const/4 v1, 0x1

    const/4 v2, 0x0

    packed-switch v0, :pswitch_data_0

    .line 94
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    throw v0

    .line 93
    :pswitch_0
    invoke-static {p0, p1}, Landroidx/compose/ui/focus/OneDimensionalFocusSearchKt;->pickChildForBackwardSearch(Landroidx/compose/ui/focus/FocusTargetNode;Lkotlin/jvm/functions/Function1;)Z

    move-result v0

    if-nez v0, :cond_2

    .line 94
    invoke-virtual {p0}, Landroidx/compose/ui/focus/FocusTargetNode;->fetchFocusProperties$ui_release()Landroidx/compose/ui/focus/FocusProperties;

    move-result-object v0

    invoke-interface {v0}, Landroidx/compose/ui/focus/FocusProperties;->getCanFocus()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Boolean;

    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v0

    goto :goto_0

    :cond_0
    move v0, v2

    :goto_0
    if-eqz v0, :cond_1

    goto :goto_1

    :cond_1
    move v1, v2

    goto :goto_3

    :cond_2
    :goto_1
    goto :goto_3

    .line 89
    :pswitch_1
    invoke-static {p0, p1}, Landroidx/compose/ui/focus/OneDimensionalFocusSearchKt;->pickChildForBackwardSearch(Landroidx/compose/ui/focus/FocusTargetNode;Lkotlin/jvm/functions/Function1;)Z

    move-result v1

    goto :goto_3

    .line 69
    :pswitch_2
    invoke-static {p0}, Landroidx/compose/ui/focus/FocusTraversalKt;->getActiveChild(Landroidx/compose/ui/focus/FocusTargetNode;)Landroidx/compose/ui/focus/FocusTargetNode;

    move-result-object v0

    const-string v3, "ActiveParent must have a focusedChild"

    if-eqz v0, :cond_5

    .line 72
    .local v0, "focusedChild":Landroidx/compose/ui/focus/FocusTargetNode;
    invoke-virtual {v0}, Landroidx/compose/ui/focus/FocusTargetNode;->getFocusState()Landroidx/compose/ui/focus/FocusStateImpl;

    move-result-object v4

    sget-object v5, Landroidx/compose/ui/focus/OneDimensionalFocusSearchKt$WhenMappings;->$EnumSwitchMapping$0:[I

    invoke-virtual {v4}, Landroidx/compose/ui/focus/FocusStateImpl;->ordinal()I

    move-result v4

    aget v4, v5, v4

    packed-switch v4, :pswitch_data_1

    .line 81
    new-instance v1, Lkotlin/NoWhenBranchMatchedException;

    invoke-direct {v1}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    throw v1

    .line 79
    :pswitch_3
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 81
    invoke-virtual {v3}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v1

    .line 79
    :pswitch_4
    sget-object v1, Landroidx/compose/ui/focus/FocusDirection;->Companion:Landroidx/compose/ui/focus/FocusDirection$Companion;

    invoke-virtual {v1}, Landroidx/compose/ui/focus/FocusDirection$Companion;->getPrevious-dhqQ-8s()I

    move-result v1

    invoke-static {p0, v0, v1, p1}, Landroidx/compose/ui/focus/OneDimensionalFocusSearchKt;->generateAndSearchChildren-4C6V_qg(Landroidx/compose/ui/focus/FocusTargetNode;Landroidx/compose/ui/focus/FocusTargetNode;ILkotlin/jvm/functions/Function1;)Z

    move-result v1

    goto :goto_3

    .line 73
    :pswitch_5
    invoke-static {v0, p1}, Landroidx/compose/ui/focus/OneDimensionalFocusSearchKt;->backwardFocusSearch(Landroidx/compose/ui/focus/FocusTargetNode;Lkotlin/jvm/functions/Function1;)Z

    move-result v3

    if-nez v3, :cond_4

    .line 74
    sget-object v3, Landroidx/compose/ui/focus/FocusDirection;->Companion:Landroidx/compose/ui/focus/FocusDirection$Companion;

    invoke-virtual {v3}, Landroidx/compose/ui/focus/FocusDirection$Companion;->getPrevious-dhqQ-8s()I

    move-result v3

    invoke-static {p0, v0, v3, p1}, Landroidx/compose/ui/focus/OneDimensionalFocusSearchKt;->generateAndSearchChildren-4C6V_qg(Landroidx/compose/ui/focus/FocusTargetNode;Landroidx/compose/ui/focus/FocusTargetNode;ILkotlin/jvm/functions/Function1;)Z

    move-result v3

    if-nez v3, :cond_4

    .line 75
    invoke-virtual {v0}, Landroidx/compose/ui/focus/FocusTargetNode;->fetchFocusProperties$ui_release()Landroidx/compose/ui/focus/FocusProperties;

    move-result-object v3

    invoke-interface {v3}, Landroidx/compose/ui/focus/FocusProperties;->getCanFocus()Z

    move-result v3

    if-eqz v3, :cond_3

    invoke-interface {p1, v0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/Boolean;

    invoke-virtual {v3}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v3

    if-eqz v3, :cond_3

    goto :goto_2

    :cond_3
    move v1, v2

    goto :goto_3

    :cond_4
    :goto_2
    nop

    .line 95
    .end local v0    # "focusedChild":Landroidx/compose/ui/focus/FocusTargetNode;
    :goto_3
    return v1

    .line 69
    :cond_5
    new-instance v0, Ljava/lang/IllegalStateException;

    invoke-virtual {v3}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0

    nop

    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_2
        :pswitch_1
        :pswitch_1
        :pswitch_0
    .end packed-switch

    :pswitch_data_1
    .packed-switch 0x1
        :pswitch_5
        :pswitch_4
        :pswitch_4
        :pswitch_3
    .end packed-switch
.end method

.method private static final forEachItemAfter(Landroidx/compose/runtime/collection/MutableVector;Ljava/lang/Object;Lkotlin/jvm/functions/Function1;)V
    .locals 7
    .param p0, "$this$forEachItemAfter"    # Landroidx/compose/runtime/collection/MutableVector;
    .param p1, "item"    # Ljava/lang/Object;
    .param p2, "action"    # Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Landroidx/compose/runtime/collection/MutableVector<",
            "TT;>;TT;",
            "Lkotlin/jvm/functions/Function1<",
            "-TT;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    const/4 v0, 0x0

    .line 181
    .local v0, "$i$f$forEachItemAfter":I
    nop

    .line 182
    const/4 v1, 0x0

    .line 183
    .local v1, "itemFound":Z
    move-object v2, p0

    .local v2, "this_$iv":Landroidx/compose/runtime/collection/MutableVector;
    const/4 v3, 0x0

    .line 601
    .local v3, "$i$f$getIndices":I
    new-instance v4, Lkotlin/ranges/IntRange;

    invoke-virtual {v2}, Landroidx/compose/runtime/collection/MutableVector;->getSize()I

    move-result v5

    add-int/lit8 v5, v5, -0x1

    const/4 v6, 0x0

    invoke-direct {v4, v6, v5}, Lkotlin/ranges/IntRange;-><init>(II)V

    .line 183
    .end local v2    # "this_$iv":Landroidx/compose/runtime/collection/MutableVector;
    .end local v3    # "$i$f$getIndices":I
    invoke-virtual {v4}, Lkotlin/ranges/IntRange;->getFirst()I

    move-result v2

    .local v2, "index":I
    invoke-virtual {v4}, Lkotlin/ranges/IntRange;->getLast()I

    move-result v3

    if-gt v2, v3, :cond_2

    .line 184
    :goto_0
    if-eqz v1, :cond_0

    .line 185
    move-object v4, p0

    .local v4, "this_$iv":Landroidx/compose/runtime/collection/MutableVector;
    const/4 v5, 0x0

    .line 602
    .local v5, "$i$f$get":I
    invoke-virtual {v4}, Landroidx/compose/runtime/collection/MutableVector;->getContent()[Ljava/lang/Object;

    move-result-object v6

    aget-object v4, v6, v2

    .line 185
    .end local v4    # "this_$iv":Landroidx/compose/runtime/collection/MutableVector;
    .end local v5    # "$i$f$get":I
    invoke-interface {p2, v4}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 187
    :cond_0
    move-object v4, p0

    .restart local v4    # "this_$iv":Landroidx/compose/runtime/collection/MutableVector;
    const/4 v5, 0x0

    .line 603
    .restart local v5    # "$i$f$get":I
    invoke-virtual {v4}, Landroidx/compose/runtime/collection/MutableVector;->getContent()[Ljava/lang/Object;

    move-result-object v6

    aget-object v4, v6, v2

    .line 187
    .end local v4    # "this_$iv":Landroidx/compose/runtime/collection/MutableVector;
    .end local v5    # "$i$f$get":I
    invoke-static {v4, p1}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_1

    .line 188
    const/4 v1, 0x1

    .line 183
    :cond_1
    if-eq v2, v3, :cond_2

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    .line 191
    .end local v2    # "index":I
    :cond_2
    return-void
.end method

.method private static final forEachItemBefore(Landroidx/compose/runtime/collection/MutableVector;Ljava/lang/Object;Lkotlin/jvm/functions/Function1;)V
    .locals 7
    .param p0, "$this$forEachItemBefore"    # Landroidx/compose/runtime/collection/MutableVector;
    .param p1, "item"    # Ljava/lang/Object;
    .param p2, "action"    # Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Landroidx/compose/runtime/collection/MutableVector<",
            "TT;>;TT;",
            "Lkotlin/jvm/functions/Function1<",
            "-TT;",
            "Lkotlin/Unit;",
            ">;)V"
        }
    .end annotation

    const/4 v0, 0x0

    .line 196
    .local v0, "$i$f$forEachItemBefore":I
    nop

    .line 197
    const/4 v1, 0x0

    .line 198
    .local v1, "itemFound":Z
    move-object v2, p0

    .local v2, "this_$iv":Landroidx/compose/runtime/collection/MutableVector;
    const/4 v3, 0x0

    .line 604
    .local v3, "$i$f$getIndices":I
    new-instance v4, Lkotlin/ranges/IntRange;

    invoke-virtual {v2}, Landroidx/compose/runtime/collection/MutableVector;->getSize()I

    move-result v5

    add-int/lit8 v5, v5, -0x1

    const/4 v6, 0x0

    invoke-direct {v4, v6, v5}, Lkotlin/ranges/IntRange;-><init>(II)V

    .line 198
    .end local v2    # "this_$iv":Landroidx/compose/runtime/collection/MutableVector;
    .end local v3    # "$i$f$getIndices":I
    invoke-virtual {v4}, Lkotlin/ranges/IntRange;->getFirst()I

    move-result v2

    invoke-virtual {v4}, Lkotlin/ranges/IntRange;->getLast()I

    move-result v3

    .local v3, "index":I
    if-gt v2, v3, :cond_2

    .line 199
    :goto_0
    if-eqz v1, :cond_0

    .line 200
    move-object v4, p0

    .local v4, "this_$iv":Landroidx/compose/runtime/collection/MutableVector;
    const/4 v5, 0x0

    .line 605
    .local v5, "$i$f$get":I
    invoke-virtual {v4}, Landroidx/compose/runtime/collection/MutableVector;->getContent()[Ljava/lang/Object;

    move-result-object v6

    aget-object v4, v6, v3

    .line 200
    .end local v4    # "this_$iv":Landroidx/compose/runtime/collection/MutableVector;
    .end local v5    # "$i$f$get":I
    invoke-interface {p2, v4}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 202
    :cond_0
    move-object v4, p0

    .restart local v4    # "this_$iv":Landroidx/compose/runtime/collection/MutableVector;
    const/4 v5, 0x0

    .line 606
    .restart local v5    # "$i$f$get":I
    invoke-virtual {v4}, Landroidx/compose/runtime/collection/MutableVector;->getContent()[Ljava/lang/Object;

    move-result-object v6

    aget-object v4, v6, v3

    .line 202
    .end local v4    # "this_$iv":Landroidx/compose/runtime/collection/MutableVector;
    .end local v5    # "$i$f$get":I
    invoke-static {v4, p1}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_1

    .line 203
    const/4 v1, 0x1

    .line 198
    :cond_1
    if-eq v3, v2, :cond_2

    add-int/lit8 v3, v3, -0x1

    goto :goto_0

    .line 206
    .end local v3    # "index":I
    :cond_2
    return-void
.end method

.method private static final forwardFocusSearch(Landroidx/compose/ui/focus/FocusTargetNode;Lkotlin/jvm/functions/Function1;)Z
    .locals 2
    .param p0, "$this$forwardFocusSearch"    # Landroidx/compose/ui/focus/FocusTargetNode;
    .param p1, "onFound"    # Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/compose/ui/focus/FocusTargetNode;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroidx/compose/ui/focus/FocusTargetNode;",
            "Ljava/lang/Boolean;",
            ">;)Z"
        }
    .end annotation

    .line 51
    invoke-virtual {p0}, Landroidx/compose/ui/focus/FocusTargetNode;->getFocusState()Landroidx/compose/ui/focus/FocusStateImpl;

    move-result-object v0

    sget-object v1, Landroidx/compose/ui/focus/OneDimensionalFocusSearchKt$WhenMappings;->$EnumSwitchMapping$0:[I

    invoke-virtual {v0}, Landroidx/compose/ui/focus/FocusStateImpl;->ordinal()I

    move-result v0

    aget v0, v1, v0

    packed-switch v0, :pswitch_data_0

    .line 61
    new-instance v0, Lkotlin/NoWhenBranchMatchedException;

    invoke-direct {v0}, Lkotlin/NoWhenBranchMatchedException;-><init>()V

    throw v0

    .line 58
    :pswitch_0
    invoke-virtual {p0}, Landroidx/compose/ui/focus/FocusTargetNode;->fetchFocusProperties$ui_release()Landroidx/compose/ui/focus/FocusProperties;

    move-result-object v0

    invoke-interface {v0}, Landroidx/compose/ui/focus/FocusProperties;->getCanFocus()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 59
    invoke-interface {p1, p0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Boolean;

    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v0

    goto :goto_1

    .line 61
    :cond_0
    invoke-static {p0, p1}, Landroidx/compose/ui/focus/OneDimensionalFocusSearchKt;->pickChildForForwardSearch(Landroidx/compose/ui/focus/FocusTargetNode;Lkotlin/jvm/functions/Function1;)Z

    move-result v0

    goto :goto_1

    .line 57
    :pswitch_1
    invoke-static {p0, p1}, Landroidx/compose/ui/focus/OneDimensionalFocusSearchKt;->pickChildForForwardSearch(Landroidx/compose/ui/focus/FocusTargetNode;Lkotlin/jvm/functions/Function1;)Z

    move-result v0

    goto :goto_1

    .line 53
    :pswitch_2
    invoke-static {p0}, Landroidx/compose/ui/focus/FocusTraversalKt;->getActiveChild(Landroidx/compose/ui/focus/FocusTargetNode;)Landroidx/compose/ui/focus/FocusTargetNode;

    move-result-object v0

    if-eqz v0, :cond_3

    .line 54
    .local v0, "focusedChild":Landroidx/compose/ui/focus/FocusTargetNode;
    invoke-static {v0, p1}, Landroidx/compose/ui/focus/OneDimensionalFocusSearchKt;->forwardFocusSearch(Landroidx/compose/ui/focus/FocusTargetNode;Lkotlin/jvm/functions/Function1;)Z

    move-result v1

    if-nez v1, :cond_2

    .line 55
    sget-object v1, Landroidx/compose/ui/focus/FocusDirection;->Companion:Landroidx/compose/ui/focus/FocusDirection$Companion;

    invoke-virtual {v1}, Landroidx/compose/ui/focus/FocusDirection$Companion;->getNext-dhqQ-8s()I

    move-result v1

    invoke-static {p0, v0, v1, p1}, Landroidx/compose/ui/focus/OneDimensionalFocusSearchKt;->generateAndSearchChildren-4C6V_qg(Landroidx/compose/ui/focus/FocusTargetNode;Landroidx/compose/ui/focus/FocusTargetNode;ILkotlin/jvm/functions/Function1;)Z

    move-result v1

    if-eqz v1, :cond_1

    goto :goto_0

    .end local v0    # "focusedChild":Landroidx/compose/ui/focus/FocusTargetNode;
    :cond_1
    const/4 v0, 0x0

    goto :goto_1

    .restart local v0    # "focusedChild":Landroidx/compose/ui/focus/FocusTargetNode;
    :cond_2
    :goto_0
    const/4 v1, 0x1

    move v0, v1

    .line 63
    .end local v0    # "focusedChild":Landroidx/compose/ui/focus/FocusTargetNode;
    :goto_1
    return v0

    .line 53
    :cond_3
    new-instance v0, Ljava/lang/IllegalStateException;

    const-string v1, "ActiveParent must have a focusedChild"

    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0

    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_2
        :pswitch_1
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method private static final generateAndSearchChildren-4C6V_qg(Landroidx/compose/ui/focus/FocusTargetNode;Landroidx/compose/ui/focus/FocusTargetNode;ILkotlin/jvm/functions/Function1;)Z
    .locals 1
    .param p0, "$this$generateAndSearchChildren_u2d4C6V_qg"    # Landroidx/compose/ui/focus/FocusTargetNode;
    .param p1, "focusedItem"    # Landroidx/compose/ui/focus/FocusTargetNode;
    .param p2, "direction"    # I
    .param p3, "onFound"    # Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/compose/ui/focus/FocusTargetNode;",
            "Landroidx/compose/ui/focus/FocusTargetNode;",
            "I",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroidx/compose/ui/focus/FocusTargetNode;",
            "Ljava/lang/Boolean;",
            ">;)Z"
        }
    .end annotation

    .line 105
    invoke-static {p0, p1, p2, p3}, Landroidx/compose/ui/focus/OneDimensionalFocusSearchKt;->searchChildren-4C6V_qg(Landroidx/compose/ui/focus/FocusTargetNode;Landroidx/compose/ui/focus/FocusTargetNode;ILkotlin/jvm/functions/Function1;)Z

    move-result v0

    if-eqz v0, :cond_0

    .line 106
    const/4 v0, 0x1

    return v0

    .line 110
    :cond_0
    new-instance v0, Landroidx/compose/ui/focus/OneDimensionalFocusSearchKt$generateAndSearchChildren$1;

    invoke-direct {v0, p0, p1, p2, p3}, Landroidx/compose/ui/focus/OneDimensionalFocusSearchKt$generateAndSearchChildren$1;-><init>(Landroidx/compose/ui/focus/FocusTargetNode;Landroidx/compose/ui/focus/FocusTargetNode;ILkotlin/jvm/functions/Function1;)V

    check-cast v0, Lkotlin/jvm/functions/Function1;

    invoke-static {p0, p2, v0}, Landroidx/compose/ui/focus/BeyondBoundsLayoutKt;->searchBeyondBounds--OM-vw8(Landroidx/compose/ui/focus/FocusTargetNode;ILkotlin/jvm/functions/Function1;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ljava/lang/Boolean;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v0

    goto :goto_0

    .line 116
    :cond_1
    const/4 v0, 0x0

    .line 110
    :goto_0
    return v0
.end method

.method private static synthetic getInvalidFocusDirection$annotations()V
    .locals 0

    return-void
.end method

.method private static synthetic getNoActiveChild$annotations()V
    .locals 0

    return-void
.end method

.method private static final isRoot(Landroidx/compose/ui/focus/FocusTargetNode;)Z
    .locals 32
    .param p0, "$this$isRoot"    # Landroidx/compose/ui/focus/FocusTargetNode;

    .line 176
    move-object/from16 v0, p0

    check-cast v0, Landroidx/compose/ui/node/DelegatableNode;

    const/4 v1, 0x0

    .line 532
    .local v1, "$i$f$getFocusTarget-OLwlOKw":I
    const/16 v2, 0x400

    invoke-static {v2}, Landroidx/compose/ui/node/NodeKind;->constructor-impl(I)I

    move-result v1

    .line 176
    .end local v1    # "$i$f$getFocusTarget-OLwlOKw":I
    nop

    .local v0, "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .local v1, "type$iv":I
    const/4 v2, 0x0

    .line 533
    .local v2, "$i$f$nearestAncestor-64DMado":I
    move-object v3, v0

    .line 534
    .local v3, "$this$visitAncestors_u2dY_u2dYKmho_u24default$iv$iv":Landroidx/compose/ui/node/DelegatableNode;
    nop

    .line 536
    const/4 v4, 0x0

    .line 534
    .local v4, "includeSelf$iv$iv":Z
    const/4 v5, 0x0

    .line 538
    .local v5, "$i$f$visitAncestors-Y-YKmho":I
    move v6, v1

    .local v6, "mask$iv$iv$iv":I
    move-object v7, v3

    .local v7, "$this$visitAncestors$iv$iv$iv":Landroidx/compose/ui/node/DelegatableNode;
    const/4 v8, 0x0

    .line 539
    .local v8, "$i$f$visitAncestors":I
    invoke-interface {v7}, Landroidx/compose/ui/node/DelegatableNode;->getNode()Landroidx/compose/ui/Modifier$Node;

    move-result-object v9

    invoke-virtual {v9}, Landroidx/compose/ui/Modifier$Node;->isAttached()Z

    move-result v9

    if-eqz v9, :cond_13

    .line 541
    invoke-interface {v7}, Landroidx/compose/ui/node/DelegatableNode;->getNode()Landroidx/compose/ui/Modifier$Node;

    move-result-object v9

    invoke-virtual {v9}, Landroidx/compose/ui/Modifier$Node;->getParent$ui_release()Landroidx/compose/ui/Modifier$Node;

    move-result-object v9

    .line 542
    .local v9, "node$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    invoke-static {v7}, Landroidx/compose/ui/node/DelegatableNodeKt;->requireLayoutNode(Landroidx/compose/ui/node/DelegatableNode;)Landroidx/compose/ui/node/LayoutNode;

    move-result-object v10

    .line 543
    .local v10, "layout$iv$iv$iv":Landroidx/compose/ui/node/LayoutNode;
    :goto_0
    const/4 v13, 0x1

    if-eqz v10, :cond_11

    .line 544
    invoke-virtual {v10}, Landroidx/compose/ui/node/LayoutNode;->getNodes$ui_release()Landroidx/compose/ui/node/NodeChain;

    move-result-object v14

    invoke-virtual {v14}, Landroidx/compose/ui/node/NodeChain;->getHead$ui_release()Landroidx/compose/ui/Modifier$Node;

    move-result-object v14

    .line 545
    .local v14, "head$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    invoke-virtual {v14}, Landroidx/compose/ui/Modifier$Node;->getAggregateChildKindSet$ui_release()I

    move-result v15

    and-int/2addr v15, v6

    if-eqz v15, :cond_f

    .line 546
    :goto_1
    if-eqz v9, :cond_e

    .line 547
    invoke-virtual {v9}, Landroidx/compose/ui/Modifier$Node;->getKindSet$ui_release()I

    move-result v15

    and-int/2addr v15, v6

    if-eqz v15, :cond_d

    .line 548
    move-object v15, v9

    .local v15, "it$iv$iv":Landroidx/compose/ui/Modifier$Node;
    const/16 v16, 0x0

    .line 538
    .local v16, "$i$a$-visitAncestors-DelegatableNodeKt$visitAncestors$2$iv$iv":I
    move-object/from16 v17, v15

    .local v17, "$this$dispatchForKind_u2d6rFNWt0$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    const/16 v18, 0x0

    .line 549
    .local v18, "$i$f$dispatchForKind-6rFNWt0":I
    const/16 v19, 0x0

    .line 550
    .local v19, "stack$iv$iv$iv":Ljava/lang/Object;
    const/16 v20, 0x0

    .local v20, "node$iv$iv$iv":Ljava/lang/Object;
    move-object/from16 v20, v17

    move-object/from16 v11, v20

    .line 551
    .end local v20    # "node$iv$iv$iv":Ljava/lang/Object;
    .local v11, "node$iv$iv$iv":Ljava/lang/Object;
    :goto_2
    if-eqz v11, :cond_c

    .line 552
    instance-of v12, v11, Landroidx/compose/ui/focus/FocusTargetNode;

    if-eqz v12, :cond_0

    .line 553
    move-object v12, v11

    .local v12, "it$iv":Ljava/lang/Object;
    const/16 v20, 0x0

    .line 554
    .local v20, "$i$a$-visitAncestors-Y-YKmho$default-DelegatableNodeKt$nearestAncestor$2$iv":I
    move v0, v13

    const/4 v2, 0x0

    goto/16 :goto_c

    .line 555
    .end local v12    # "it$iv":Ljava/lang/Object;
    .end local v20    # "$i$a$-visitAncestors-Y-YKmho$default-DelegatableNodeKt$nearestAncestor$2$iv":I
    :cond_0
    move-object v12, v11

    .local v12, "this_$iv$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    const/16 v21, 0x0

    .line 556
    .local v21, "$i$f$isKind-H91voCI$ui_release":I
    invoke-virtual {v12}, Landroidx/compose/ui/Modifier$Node;->getKindSet$ui_release()I

    move-result v22

    and-int v22, v22, v1

    if-eqz v22, :cond_1

    move v12, v13

    goto :goto_3

    :cond_1
    const/4 v12, 0x0

    .line 555
    .end local v12    # "this_$iv$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v21    # "$i$f$isKind-H91voCI$ui_release":I
    :goto_3
    if-eqz v12, :cond_a

    instance-of v12, v11, Landroidx/compose/ui/node/DelegatingNode;

    if-eqz v12, :cond_a

    .line 557
    const/4 v12, 0x0

    .line 558
    .local v12, "count$iv$iv$iv":I
    move-object/from16 v21, v11

    check-cast v21, Landroidx/compose/ui/node/DelegatingNode;

    .local v21, "this_$iv$iv$iv$iv":Landroidx/compose/ui/node/DelegatingNode;
    const/16 v22, 0x0

    .line 559
    .local v22, "$i$f$forEachImmediateDelegate$ui_release":I
    invoke-virtual/range {v21 .. v21}, Landroidx/compose/ui/node/DelegatingNode;->getDelegate$ui_release()Landroidx/compose/ui/Modifier$Node;

    move-result-object v23

    .line 560
    .local v23, "node$iv$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    :goto_4
    if-eqz v23, :cond_9

    .line 561
    move-object/from16 v24, v23

    .local v24, "next$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    const/16 v25, 0x0

    .line 562
    .local v25, "$i$a$-forEachImmediateDelegate$ui_release-DelegatableNodeKt$dispatchForKind$1$iv$iv$iv":I
    move-object/from16 v26, v24

    .local v26, "this_$iv$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    const/16 v27, 0x0

    .line 556
    .local v27, "$i$f$isKind-H91voCI$ui_release":I
    invoke-virtual/range {v26 .. v26}, Landroidx/compose/ui/Modifier$Node;->getKindSet$ui_release()I

    move-result v28

    and-int v28, v28, v1

    if-eqz v28, :cond_2

    move/from16 v26, v13

    goto :goto_5

    :cond_2
    const/16 v26, 0x0

    .line 562
    .end local v26    # "this_$iv$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v27    # "$i$f$isKind-H91voCI$ui_release":I
    :goto_5
    if-eqz v26, :cond_8

    .line 563
    add-int/lit8 v12, v12, 0x1

    .line 564
    if-ne v12, v13, :cond_3

    .line 565
    move-object/from16 v11, v24

    move-object/from16 v29, v0

    move/from16 v30, v1

    move/from16 v31, v2

    move-object/from16 v13, v24

    const/4 v2, 0x0

    goto :goto_8

    .line 569
    :cond_3
    if-nez v19, :cond_4

    const/16 v26, 0x0

    .line 570
    .local v26, "$i$f$mutableVectorOf":I
    nop

    .line 571
    const/16 v13, 0x10

    .local v13, "capacity$iv$iv$iv$iv$iv":I
    const/16 v28, 0x0

    .line 572
    .local v28, "$i$f$MutableVector":I
    move-object/from16 v29, v0

    .end local v0    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .local v29, "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    new-instance v0, Landroidx/compose/runtime/collection/MutableVector;

    move/from16 v30, v1

    .end local v1    # "type$iv":I
    .local v30, "type$iv":I
    new-array v1, v13, [Landroidx/compose/ui/Modifier$Node;

    move/from16 v31, v2

    const/4 v2, 0x0

    .end local v2    # "$i$f$nearestAncestor-64DMado":I
    .local v31, "$i$f$nearestAncestor-64DMado":I
    invoke-direct {v0, v1, v2}, Landroidx/compose/runtime/collection/MutableVector;-><init>([Ljava/lang/Object;I)V

    .line 570
    .end local v13    # "capacity$iv$iv$iv$iv$iv":I
    .end local v28    # "$i$f$MutableVector":I
    goto :goto_6

    .line 569
    .end local v26    # "$i$f$mutableVectorOf":I
    .end local v29    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v30    # "type$iv":I
    .end local v31    # "$i$f$nearestAncestor-64DMado":I
    .restart local v0    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v1    # "type$iv":I
    .restart local v2    # "$i$f$nearestAncestor-64DMado":I
    :cond_4
    move-object/from16 v29, v0

    move/from16 v30, v1

    move/from16 v31, v2

    const/4 v2, 0x0

    .end local v0    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v1    # "type$iv":I
    .end local v2    # "$i$f$nearestAncestor-64DMado":I
    .restart local v29    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v30    # "type$iv":I
    .restart local v31    # "$i$f$nearestAncestor-64DMado":I
    move-object/from16 v0, v19

    :goto_6
    nop

    .line 573
    .end local v19    # "stack$iv$iv$iv":Ljava/lang/Object;
    .local v0, "stack$iv$iv$iv":Ljava/lang/Object;
    move-object v1, v11

    .line 574
    .local v1, "theNode$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    if-eqz v1, :cond_6

    .line 575
    if-eqz v0, :cond_5

    invoke-virtual {v0, v1}, Landroidx/compose/runtime/collection/MutableVector;->add(Ljava/lang/Object;)Z

    .line 576
    :cond_5
    const/4 v11, 0x0

    .line 578
    :cond_6
    if-eqz v0, :cond_7

    move-object/from16 v13, v24

    .end local v24    # "next$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .local v13, "next$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    invoke-virtual {v0, v13}, Landroidx/compose/runtime/collection/MutableVector;->add(Ljava/lang/Object;)Z

    goto :goto_7

    .end local v13    # "next$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .restart local v24    # "next$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    :cond_7
    move-object/from16 v13, v24

    .line 581
    .end local v1    # "theNode$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v24    # "next$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .restart local v13    # "next$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    :goto_7
    move-object/from16 v19, v0

    goto :goto_8

    .line 562
    .end local v13    # "next$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v29    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v30    # "type$iv":I
    .end local v31    # "$i$f$nearestAncestor-64DMado":I
    .local v0, "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .local v1, "type$iv":I
    .restart local v2    # "$i$f$nearestAncestor-64DMado":I
    .restart local v19    # "stack$iv$iv$iv":Ljava/lang/Object;
    .restart local v24    # "next$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    :cond_8
    move-object/from16 v29, v0

    move/from16 v30, v1

    move/from16 v31, v2

    move-object/from16 v13, v24

    const/4 v2, 0x0

    .line 581
    .end local v0    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v1    # "type$iv":I
    .end local v2    # "$i$f$nearestAncestor-64DMado":I
    .end local v24    # "next$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .restart local v13    # "next$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .restart local v29    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v30    # "type$iv":I
    .restart local v31    # "$i$f$nearestAncestor-64DMado":I
    :goto_8
    nop

    .line 561
    .end local v13    # "next$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v25    # "$i$a$-forEachImmediateDelegate$ui_release-DelegatableNodeKt$dispatchForKind$1$iv$iv$iv":I
    nop

    .line 582
    invoke-virtual/range {v23 .. v23}, Landroidx/compose/ui/Modifier$Node;->getChild$ui_release()Landroidx/compose/ui/Modifier$Node;

    move-result-object v23

    move-object/from16 v0, v29

    move/from16 v1, v30

    move/from16 v2, v31

    const/4 v13, 0x1

    goto :goto_4

    .line 584
    .end local v29    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v30    # "type$iv":I
    .end local v31    # "$i$f$nearestAncestor-64DMado":I
    .restart local v0    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v1    # "type$iv":I
    .restart local v2    # "$i$f$nearestAncestor-64DMado":I
    :cond_9
    move-object/from16 v29, v0

    move/from16 v30, v1

    move/from16 v31, v2

    const/4 v2, 0x0

    .line 585
    .end local v0    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v1    # "type$iv":I
    .end local v2    # "$i$f$nearestAncestor-64DMado":I
    .end local v21    # "this_$iv$iv$iv$iv":Landroidx/compose/ui/node/DelegatingNode;
    .end local v22    # "$i$f$forEachImmediateDelegate$ui_release":I
    .end local v23    # "node$iv$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .restart local v29    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v30    # "type$iv":I
    .restart local v31    # "$i$f$nearestAncestor-64DMado":I
    const/4 v0, 0x1

    if-ne v12, v0, :cond_b

    .line 587
    move v13, v0

    move-object/from16 v0, v29

    move/from16 v1, v30

    move/from16 v2, v31

    goto/16 :goto_2

    .line 555
    .end local v12    # "count$iv$iv$iv":I
    .end local v29    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v30    # "type$iv":I
    .end local v31    # "$i$f$nearestAncestor-64DMado":I
    .restart local v0    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v1    # "type$iv":I
    .restart local v2    # "$i$f$nearestAncestor-64DMado":I
    :cond_a
    move-object/from16 v29, v0

    move/from16 v30, v1

    move/from16 v31, v2

    move v0, v13

    const/4 v2, 0x0

    .line 590
    .end local v0    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v1    # "type$iv":I
    .end local v2    # "$i$f$nearestAncestor-64DMado":I
    .restart local v29    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v30    # "type$iv":I
    .restart local v31    # "$i$f$nearestAncestor-64DMado":I
    :cond_b
    invoke-static/range {v19 .. v19}, Landroidx/compose/ui/node/DelegatableNodeKt;->access$pop(Landroidx/compose/runtime/collection/MutableVector;)Landroidx/compose/ui/Modifier$Node;

    move-result-object v11

    move v13, v0

    move-object/from16 v0, v29

    move/from16 v1, v30

    move/from16 v2, v31

    goto/16 :goto_2

    .line 592
    .end local v29    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v30    # "type$iv":I
    .end local v31    # "$i$f$nearestAncestor-64DMado":I
    .restart local v0    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v1    # "type$iv":I
    .restart local v2    # "$i$f$nearestAncestor-64DMado":I
    :cond_c
    move-object/from16 v29, v0

    move/from16 v30, v1

    move/from16 v31, v2

    move v0, v13

    const/4 v2, 0x0

    .line 538
    .end local v0    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v1    # "type$iv":I
    .end local v2    # "$i$f$nearestAncestor-64DMado":I
    .end local v11    # "node$iv$iv$iv":Ljava/lang/Object;
    .end local v17    # "$this$dispatchForKind_u2d6rFNWt0$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v18    # "$i$f$dispatchForKind-6rFNWt0":I
    .end local v19    # "stack$iv$iv$iv":Ljava/lang/Object;
    .restart local v29    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v30    # "type$iv":I
    .restart local v31    # "$i$f$nearestAncestor-64DMado":I
    nop

    .line 548
    .end local v15    # "it$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v16    # "$i$a$-visitAncestors-DelegatableNodeKt$visitAncestors$2$iv$iv":I
    goto :goto_9

    .line 547
    .end local v29    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v30    # "type$iv":I
    .end local v31    # "$i$f$nearestAncestor-64DMado":I
    .restart local v0    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v1    # "type$iv":I
    .restart local v2    # "$i$f$nearestAncestor-64DMado":I
    :cond_d
    move-object/from16 v29, v0

    move/from16 v30, v1

    move/from16 v31, v2

    move v0, v13

    const/4 v2, 0x0

    .line 593
    .end local v0    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v1    # "type$iv":I
    .end local v2    # "$i$f$nearestAncestor-64DMado":I
    .restart local v29    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v30    # "type$iv":I
    .restart local v31    # "$i$f$nearestAncestor-64DMado":I
    :goto_9
    invoke-virtual {v9}, Landroidx/compose/ui/Modifier$Node;->getParent$ui_release()Landroidx/compose/ui/Modifier$Node;

    move-result-object v9

    move v13, v0

    move-object/from16 v0, v29

    move/from16 v1, v30

    move/from16 v2, v31

    goto/16 :goto_1

    .line 546
    .end local v29    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v30    # "type$iv":I
    .end local v31    # "$i$f$nearestAncestor-64DMado":I
    .restart local v0    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v1    # "type$iv":I
    .restart local v2    # "$i$f$nearestAncestor-64DMado":I
    :cond_e
    move-object/from16 v29, v0

    move/from16 v30, v1

    move/from16 v31, v2

    .end local v0    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v1    # "type$iv":I
    .end local v2    # "$i$f$nearestAncestor-64DMado":I
    .restart local v29    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v30    # "type$iv":I
    .restart local v31    # "$i$f$nearestAncestor-64DMado":I
    goto :goto_a

    .line 545
    .end local v29    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v30    # "type$iv":I
    .end local v31    # "$i$f$nearestAncestor-64DMado":I
    .restart local v0    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v1    # "type$iv":I
    .restart local v2    # "$i$f$nearestAncestor-64DMado":I
    :cond_f
    move-object/from16 v29, v0

    move/from16 v30, v1

    move/from16 v31, v2

    .line 596
    .end local v0    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v1    # "type$iv":I
    .end local v2    # "$i$f$nearestAncestor-64DMado":I
    .restart local v29    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v30    # "type$iv":I
    .restart local v31    # "$i$f$nearestAncestor-64DMado":I
    :goto_a
    invoke-virtual {v10}, Landroidx/compose/ui/node/LayoutNode;->getParent$ui_release()Landroidx/compose/ui/node/LayoutNode;

    move-result-object v10

    .line 597
    if-eqz v10, :cond_10

    invoke-virtual {v10}, Landroidx/compose/ui/node/LayoutNode;->getNodes$ui_release()Landroidx/compose/ui/node/NodeChain;

    move-result-object v0

    if-eqz v0, :cond_10

    invoke-virtual {v0}, Landroidx/compose/ui/node/NodeChain;->getTail$ui_release()Landroidx/compose/ui/Modifier$Node;

    move-result-object v11

    goto :goto_b

    :cond_10
    const/4 v11, 0x0

    :goto_b
    move-object v9, v11

    move-object/from16 v0, v29

    move/from16 v1, v30

    move/from16 v2, v31

    .end local v14    # "head$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    goto/16 :goto_0

    .line 599
    .end local v29    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v30    # "type$iv":I
    .end local v31    # "$i$f$nearestAncestor-64DMado":I
    .restart local v0    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v1    # "type$iv":I
    .restart local v2    # "$i$f$nearestAncestor-64DMado":I
    :cond_11
    move-object/from16 v29, v0

    move/from16 v30, v1

    move/from16 v31, v2

    move v0, v13

    const/4 v2, 0x0

    .line 538
    .end local v0    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v1    # "type$iv":I
    .end local v2    # "$i$f$nearestAncestor-64DMado":I
    .end local v6    # "mask$iv$iv$iv":I
    .end local v7    # "$this$visitAncestors$iv$iv$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v8    # "$i$f$visitAncestors":I
    .end local v9    # "node$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v10    # "layout$iv$iv$iv":Landroidx/compose/ui/node/LayoutNode;
    .restart local v29    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v30    # "type$iv":I
    .restart local v31    # "$i$f$nearestAncestor-64DMado":I
    nop

    .line 600
    .end local v3    # "$this$visitAncestors_u2dY_u2dYKmho_u24default$iv$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v4    # "includeSelf$iv$iv":Z
    .end local v5    # "$i$f$visitAncestors-Y-YKmho":I
    const/4 v11, 0x0

    .end local v29    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v30    # "type$iv":I
    .end local v31    # "$i$f$nearestAncestor-64DMado":I
    :goto_c
    if-nez v11, :cond_12

    move v12, v0

    goto :goto_d

    :cond_12
    move v12, v2

    .line 176
    :goto_d
    return v12

    .line 540
    .restart local v0    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v1    # "type$iv":I
    .restart local v2    # "$i$f$nearestAncestor-64DMado":I
    .restart local v3    # "$this$visitAncestors_u2dY_u2dYKmho_u24default$iv$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v4    # "includeSelf$iv$iv":Z
    .restart local v5    # "$i$f$visitAncestors-Y-YKmho":I
    .restart local v6    # "mask$iv$iv$iv":I
    .restart local v7    # "$this$visitAncestors$iv$iv$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v8    # "$i$f$visitAncestors":I
    :cond_13
    move-object/from16 v29, v0

    move/from16 v30, v1

    .end local v0    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v1    # "type$iv":I
    .restart local v29    # "$this$nearestAncestor_u2d64DMado$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v30    # "type$iv":I
    const/4 v0, 0x0

    .line 539
    .local v0, "$i$a$-check-DelegatableNodeKt$visitAncestors$1$iv$iv$iv":I
    nop

    .end local v0    # "$i$a$-check-DelegatableNodeKt$visitAncestors$1$iv$iv$iv":I
    new-instance v0, Ljava/lang/IllegalStateException;

    const-string/jumbo v1, "visitAncestors called on an unattached node"

    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public static final oneDimensionalFocusSearch--OM-vw8(Landroidx/compose/ui/focus/FocusTargetNode;ILkotlin/jvm/functions/Function1;)Z
    .locals 2
    .param p0, "$this$oneDimensionalFocusSearch_u2d_u2dOM_u2dvw8"    # Landroidx/compose/ui/focus/FocusTargetNode;
    .param p1, "direction"    # I
    .param p2, "onFound"    # Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/compose/ui/focus/FocusTargetNode;",
            "I",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroidx/compose/ui/focus/FocusTargetNode;",
            "Ljava/lang/Boolean;",
            ">;)Z"
        }
    .end annotation

    .line 43
    nop

    .line 44
    sget-object v0, Landroidx/compose/ui/focus/FocusDirection;->Companion:Landroidx/compose/ui/focus/FocusDirection$Companion;

    invoke-virtual {v0}, Landroidx/compose/ui/focus/FocusDirection$Companion;->getNext-dhqQ-8s()I

    move-result v0

    invoke-static {p1, v0}, Landroidx/compose/ui/focus/FocusDirection;->equals-impl0(II)Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-static {p0, p2}, Landroidx/compose/ui/focus/OneDimensionalFocusSearchKt;->forwardFocusSearch(Landroidx/compose/ui/focus/FocusTargetNode;Lkotlin/jvm/functions/Function1;)Z

    move-result v0

    goto :goto_0

    .line 45
    :cond_0
    sget-object v0, Landroidx/compose/ui/focus/FocusDirection;->Companion:Landroidx/compose/ui/focus/FocusDirection$Companion;

    invoke-virtual {v0}, Landroidx/compose/ui/focus/FocusDirection$Companion;->getPrevious-dhqQ-8s()I

    move-result v0

    invoke-static {p1, v0}, Landroidx/compose/ui/focus/FocusDirection;->equals-impl0(II)Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-static {p0, p2}, Landroidx/compose/ui/focus/OneDimensionalFocusSearchKt;->backwardFocusSearch(Landroidx/compose/ui/focus/FocusTargetNode;Lkotlin/jvm/functions/Function1;)Z

    move-result v0

    .line 47
    :goto_0
    return v0

    .line 45
    :cond_1
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 46
    const-string v1, "This function should only be used for 1-D focus search"

    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method private static final pickChildForBackwardSearch(Landroidx/compose/ui/focus/FocusTargetNode;Lkotlin/jvm/functions/Function1;)Z
    .locals 32
    .param p0, "$this$pickChildForBackwardSearch"    # Landroidx/compose/ui/focus/FocusTargetNode;
    .param p1, "onFound"    # Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/compose/ui/focus/FocusTargetNode;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroidx/compose/ui/focus/FocusTargetNode;",
            "Ljava/lang/Boolean;",
            ">;)Z"
        }
    .end annotation

    .line 164
    nop

    .line 448
    const/16 v0, 0x10

    .local v0, "capacity$iv":I
    const/4 v1, 0x0

    .line 449
    .local v1, "$i$f$MutableVector":I
    new-instance v2, Landroidx/compose/runtime/collection/MutableVector;

    new-array v3, v0, [Landroidx/compose/ui/focus/FocusTargetNode;

    const/4 v4, 0x0

    invoke-direct {v2, v3, v4}, Landroidx/compose/runtime/collection/MutableVector;-><init>([Ljava/lang/Object;I)V

    .line 164
    .end local v0    # "capacity$iv":I
    .end local v1    # "$i$f$MutableVector":I
    move-object v0, v2

    .local v0, "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    const/4 v1, 0x0

    .line 165
    .local v1, "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    move-object/from16 v3, p0

    check-cast v3, Landroidx/compose/ui/node/DelegatableNode;

    const/4 v5, 0x0

    .line 450
    .local v5, "$i$f$getFocusTarget-OLwlOKw":I
    const/16 v6, 0x400

    invoke-static {v6}, Landroidx/compose/ui/node/NodeKind;->constructor-impl(I)I

    move-result v5

    .line 165
    .end local v5    # "$i$f$getFocusTarget-OLwlOKw":I
    nop

    .local v3, "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .local v5, "type$iv":I
    const/4 v6, 0x0

    .line 451
    .local v6, "$i$f$visitChildren-6rFNWt0":I
    move v7, v5

    .local v7, "mask$iv$iv":I
    move-object v8, v3

    .local v8, "$this$visitChildren$iv$iv":Landroidx/compose/ui/node/DelegatableNode;
    const/4 v9, 0x0

    .line 452
    .local v9, "$i$f$visitChildren":I
    invoke-interface {v8}, Landroidx/compose/ui/node/DelegatableNode;->getNode()Landroidx/compose/ui/Modifier$Node;

    move-result-object v10

    invoke-virtual {v10}, Landroidx/compose/ui/Modifier$Node;->isAttached()Z

    move-result v10

    if-eqz v10, :cond_16

    .line 454
    const/4 v10, 0x0

    .line 455
    .local v10, "$i$f$mutableVectorOf":I
    nop

    .line 456
    const/16 v11, 0x10

    .local v11, "capacity$iv$iv$iv$iv":I
    const/4 v12, 0x0

    .line 457
    .local v12, "$i$f$MutableVector":I
    new-instance v13, Landroidx/compose/runtime/collection/MutableVector;

    new-array v14, v11, [Landroidx/compose/ui/Modifier$Node;

    invoke-direct {v13, v14, v4}, Landroidx/compose/runtime/collection/MutableVector;-><init>([Ljava/lang/Object;I)V

    .line 455
    .end local v11    # "capacity$iv$iv$iv$iv":I
    .end local v12    # "$i$f$MutableVector":I
    nop

    .line 454
    .end local v10    # "$i$f$mutableVectorOf":I
    move-object v10, v13

    .line 458
    .local v10, "branches$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    invoke-interface {v8}, Landroidx/compose/ui/node/DelegatableNode;->getNode()Landroidx/compose/ui/Modifier$Node;

    move-result-object v11

    invoke-virtual {v11}, Landroidx/compose/ui/Modifier$Node;->getChild$ui_release()Landroidx/compose/ui/Modifier$Node;

    move-result-object v11

    .line 459
    .local v11, "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    if-nez v11, :cond_0

    .line 460
    invoke-interface {v8}, Landroidx/compose/ui/node/DelegatableNode;->getNode()Landroidx/compose/ui/Modifier$Node;

    move-result-object v12

    invoke-static {v10, v12}, Landroidx/compose/ui/node/DelegatableNodeKt;->access$addLayoutNodeChildren(Landroidx/compose/runtime/collection/MutableVector;Landroidx/compose/ui/Modifier$Node;)V

    goto :goto_0

    .line 462
    :cond_0
    invoke-virtual {v10, v11}, Landroidx/compose/runtime/collection/MutableVector;->add(Ljava/lang/Object;)Z

    .line 463
    :goto_0
    invoke-virtual {v10}, Landroidx/compose/runtime/collection/MutableVector;->isNotEmpty()Z

    move-result v12

    const/4 v13, 0x1

    if-eqz v12, :cond_11

    .line 464
    move-object v12, v10

    .local v12, "this_$iv$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    const/4 v14, 0x0

    .line 465
    .local v14, "$i$f$getLastIndex":I
    invoke-virtual {v12}, Landroidx/compose/runtime/collection/MutableVector;->getSize()I

    move-result v15

    sub-int/2addr v15, v13

    .line 464
    .end local v12    # "this_$iv$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    .end local v14    # "$i$f$getLastIndex":I
    invoke-virtual {v10, v15}, Landroidx/compose/runtime/collection/MutableVector;->removeAt(I)Ljava/lang/Object;

    move-result-object v12

    check-cast v12, Landroidx/compose/ui/Modifier$Node;

    .line 466
    .local v12, "branch$iv$iv":Landroidx/compose/ui/Modifier$Node;
    invoke-virtual {v12}, Landroidx/compose/ui/Modifier$Node;->getAggregateChildKindSet$ui_release()I

    move-result v14

    and-int/2addr v14, v7

    if-nez v14, :cond_1

    .line 467
    invoke-static {v10, v12}, Landroidx/compose/ui/node/DelegatableNodeKt;->access$addLayoutNodeChildren(Landroidx/compose/runtime/collection/MutableVector;Landroidx/compose/ui/Modifier$Node;)V

    .line 469
    goto :goto_0

    .line 471
    :cond_1
    move-object v14, v12

    .line 472
    .local v14, "node$iv$iv":Landroidx/compose/ui/Modifier$Node;
    :goto_1
    if-eqz v14, :cond_10

    .line 473
    invoke-virtual {v14}, Landroidx/compose/ui/Modifier$Node;->getKindSet$ui_release()I

    move-result v15

    and-int/2addr v15, v7

    if-eqz v15, :cond_f

    .line 474
    move-object v15, v14

    .local v15, "it$iv":Landroidx/compose/ui/Modifier$Node;
    const/16 v16, 0x0

    .line 451
    .local v16, "$i$a$-visitChildren-DelegatableNodeKt$visitChildren$2$iv":I
    move-object/from16 v17, v15

    .local v17, "$this$dispatchForKind_u2d6rFNWt0$iv$iv":Landroidx/compose/ui/Modifier$Node;
    const/16 v18, 0x0

    .line 475
    .local v18, "$i$f$dispatchForKind-6rFNWt0":I
    const/16 v19, 0x0

    .line 476
    .local v19, "stack$iv$iv":Ljava/lang/Object;
    const/16 v20, 0x0

    .local v20, "node$iv$iv":Ljava/lang/Object;
    move-object/from16 v20, v17

    move-object/from16 v4, v20

    .line 477
    .end local v20    # "node$iv$iv":Ljava/lang/Object;
    .local v4, "node$iv$iv":Ljava/lang/Object;
    :goto_2
    if-eqz v4, :cond_e

    .line 478
    instance-of v13, v4, Landroidx/compose/ui/focus/FocusTargetNode;

    if-eqz v13, :cond_2

    .line 479
    move-object v13, v4

    check-cast v13, Landroidx/compose/ui/focus/FocusTargetNode;

    .local v13, "it":Landroidx/compose/ui/focus/FocusTargetNode;
    const/16 v21, 0x0

    .line 165
    .local v21, "$i$a$-visitChildren-6rFNWt0-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1$1":I
    invoke-virtual {v0, v13}, Landroidx/compose/runtime/collection/MutableVector;->add(Ljava/lang/Object;)Z

    .line 479
    .end local v13    # "it":Landroidx/compose/ui/focus/FocusTargetNode;
    .end local v21    # "$i$a$-visitChildren-6rFNWt0-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1$1":I
    move-object/from16 v26, v0

    move/from16 v29, v1

    move-object/from16 v30, v3

    goto/16 :goto_9

    .line 480
    :cond_2
    move-object v13, v4

    .local v13, "this_$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    const/16 v21, 0x0

    .line 481
    .local v21, "$i$f$isKind-H91voCI$ui_release":I
    invoke-virtual {v13}, Landroidx/compose/ui/Modifier$Node;->getKindSet$ui_release()I

    move-result v22

    and-int v22, v22, v5

    if-eqz v22, :cond_3

    const/4 v13, 0x1

    goto :goto_3

    :cond_3
    const/4 v13, 0x0

    .line 480
    .end local v13    # "this_$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v21    # "$i$f$isKind-H91voCI$ui_release":I
    :goto_3
    if-eqz v13, :cond_c

    instance-of v13, v4, Landroidx/compose/ui/node/DelegatingNode;

    if-eqz v13, :cond_c

    .line 482
    const/4 v13, 0x0

    .line 483
    .local v13, "count$iv$iv":I
    move-object/from16 v21, v4

    check-cast v21, Landroidx/compose/ui/node/DelegatingNode;

    .local v21, "this_$iv$iv$iv":Landroidx/compose/ui/node/DelegatingNode;
    const/16 v22, 0x0

    .line 484
    .local v22, "$i$f$forEachImmediateDelegate$ui_release":I
    invoke-virtual/range {v21 .. v21}, Landroidx/compose/ui/node/DelegatingNode;->getDelegate$ui_release()Landroidx/compose/ui/Modifier$Node;

    move-result-object v23

    .line 485
    .local v23, "node$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    :goto_4
    if-eqz v23, :cond_b

    .line 486
    move-object/from16 v24, v23

    .local v24, "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    const/16 v25, 0x0

    .line 487
    .local v25, "$i$a$-forEachImmediateDelegate$ui_release-DelegatableNodeKt$dispatchForKind$1$iv$iv":I
    move-object/from16 v26, v24

    .local v26, "this_$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    const/16 v27, 0x0

    .line 481
    .local v27, "$i$f$isKind-H91voCI$ui_release":I
    invoke-virtual/range {v26 .. v26}, Landroidx/compose/ui/Modifier$Node;->getKindSet$ui_release()I

    move-result v28

    and-int v28, v28, v5

    if-eqz v28, :cond_4

    const/16 v26, 0x1

    goto :goto_5

    :cond_4
    const/16 v26, 0x0

    .line 487
    .end local v26    # "this_$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v27    # "$i$f$isKind-H91voCI$ui_release":I
    :goto_5
    if-eqz v26, :cond_a

    .line 488
    add-int/lit8 v13, v13, 0x1

    .line 489
    move-object/from16 v26, v0

    const/4 v0, 0x1

    .end local v0    # "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .local v26, "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    if-ne v13, v0, :cond_5

    .line 490
    move-object/from16 v4, v24

    move/from16 v29, v1

    move-object/from16 v30, v3

    move-object/from16 v3, v24

    goto :goto_8

    .line 494
    :cond_5
    if-nez v19, :cond_6

    const/4 v0, 0x0

    .line 455
    .local v0, "$i$f$mutableVectorOf":I
    nop

    .line 456
    move/from16 v27, v0

    .end local v0    # "$i$f$mutableVectorOf":I
    .local v27, "$i$f$mutableVectorOf":I
    const/16 v0, 0x10

    .local v0, "capacity$iv$iv$iv$iv":I
    const/16 v28, 0x0

    .line 457
    .local v28, "$i$f$MutableVector":I
    move/from16 v29, v1

    .end local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .local v29, "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    new-instance v1, Landroidx/compose/runtime/collection/MutableVector;

    move-object/from16 v30, v3

    .end local v3    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .local v30, "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    new-array v3, v0, [Landroidx/compose/ui/Modifier$Node;

    move/from16 v31, v0

    const/4 v0, 0x0

    .end local v0    # "capacity$iv$iv$iv$iv":I
    .local v31, "capacity$iv$iv$iv$iv":I
    invoke-direct {v1, v3, v0}, Landroidx/compose/runtime/collection/MutableVector;-><init>([Ljava/lang/Object;I)V

    .line 455
    .end local v28    # "$i$f$MutableVector":I
    .end local v31    # "capacity$iv$iv$iv$iv":I
    goto :goto_6

    .line 494
    .end local v27    # "$i$f$mutableVectorOf":I
    .end local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .end local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .restart local v3    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    :cond_6
    move/from16 v29, v1

    move-object/from16 v30, v3

    .end local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .end local v3    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .restart local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    move-object/from16 v1, v19

    :goto_6
    move-object v0, v1

    .line 495
    .end local v19    # "stack$iv$iv":Ljava/lang/Object;
    .local v0, "stack$iv$iv":Ljava/lang/Object;
    move-object v1, v4

    .line 496
    .local v1, "theNode$iv$iv":Landroidx/compose/ui/Modifier$Node;
    if-eqz v1, :cond_8

    .line 497
    if-eqz v0, :cond_7

    invoke-virtual {v0, v1}, Landroidx/compose/runtime/collection/MutableVector;->add(Ljava/lang/Object;)Z

    .line 498
    :cond_7
    const/4 v3, 0x0

    move-object v4, v3

    .line 500
    :cond_8
    if-eqz v0, :cond_9

    move-object/from16 v3, v24

    .end local v24    # "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .local v3, "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    invoke-virtual {v0, v3}, Landroidx/compose/runtime/collection/MutableVector;->add(Ljava/lang/Object;)Z

    goto :goto_7

    .end local v3    # "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .restart local v24    # "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    :cond_9
    move-object/from16 v3, v24

    .line 503
    .end local v1    # "theNode$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v24    # "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .restart local v3    # "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    :goto_7
    move-object/from16 v19, v0

    goto :goto_8

    .line 487
    .end local v26    # "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .end local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .end local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .local v0, "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .local v1, "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .local v3, "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v19    # "stack$iv$iv":Ljava/lang/Object;
    .restart local v24    # "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    :cond_a
    move-object/from16 v26, v0

    move/from16 v29, v1

    move-object/from16 v30, v3

    move-object/from16 v3, v24

    .line 503
    .end local v0    # "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .end local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .end local v24    # "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .local v3, "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .restart local v26    # "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .restart local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    :goto_8
    nop

    .line 486
    .end local v3    # "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v25    # "$i$a$-forEachImmediateDelegate$ui_release-DelegatableNodeKt$dispatchForKind$1$iv$iv":I
    nop

    .line 504
    invoke-virtual/range {v23 .. v23}, Landroidx/compose/ui/Modifier$Node;->getChild$ui_release()Landroidx/compose/ui/Modifier$Node;

    move-result-object v23

    move-object/from16 v0, v26

    move/from16 v1, v29

    move-object/from16 v3, v30

    goto :goto_4

    .line 506
    .end local v26    # "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .end local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .end local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v0    # "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .local v3, "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    :cond_b
    move-object/from16 v26, v0

    move/from16 v29, v1

    move-object/from16 v30, v3

    .line 507
    .end local v0    # "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .end local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .end local v3    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v21    # "this_$iv$iv$iv":Landroidx/compose/ui/node/DelegatingNode;
    .end local v22    # "$i$f$forEachImmediateDelegate$ui_release":I
    .end local v23    # "node$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .restart local v26    # "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .restart local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    const/4 v0, 0x1

    if-ne v13, v0, :cond_d

    .line 509
    move-object/from16 v0, v26

    move/from16 v1, v29

    move-object/from16 v3, v30

    const/4 v13, 0x1

    goto/16 :goto_2

    .line 480
    .end local v13    # "count$iv$iv":I
    .end local v26    # "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .end local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .end local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v0    # "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .restart local v3    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    :cond_c
    move-object/from16 v26, v0

    move/from16 v29, v1

    move-object/from16 v30, v3

    .line 512
    .end local v0    # "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .end local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .end local v3    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v26    # "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .restart local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    :cond_d
    :goto_9
    invoke-static/range {v19 .. v19}, Landroidx/compose/ui/node/DelegatableNodeKt;->access$pop(Landroidx/compose/runtime/collection/MutableVector;)Landroidx/compose/ui/Modifier$Node;

    move-result-object v4

    move-object/from16 v0, v26

    move/from16 v1, v29

    move-object/from16 v3, v30

    const/4 v13, 0x1

    goto/16 :goto_2

    .line 514
    .end local v26    # "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .end local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .end local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v0    # "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .restart local v3    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    :cond_e
    move-object/from16 v26, v0

    move/from16 v29, v1

    move-object/from16 v30, v3

    .line 451
    .end local v0    # "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .end local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .end local v3    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v4    # "node$iv$iv":Ljava/lang/Object;
    .end local v17    # "$this$dispatchForKind_u2d6rFNWt0$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v18    # "$i$f$dispatchForKind-6rFNWt0":I
    .end local v19    # "stack$iv$iv":Ljava/lang/Object;
    .restart local v26    # "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .restart local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    nop

    .line 474
    .end local v15    # "it$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v16    # "$i$a$-visitChildren-DelegatableNodeKt$visitChildren$2$iv":I
    nop

    .line 515
    const/4 v4, 0x0

    goto/16 :goto_0

    .line 517
    .end local v26    # "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .end local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .end local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v0    # "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .restart local v3    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    :cond_f
    move-object/from16 v26, v0

    move/from16 v29, v1

    move-object/from16 v30, v3

    .end local v0    # "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .end local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .end local v3    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v26    # "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .restart local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    invoke-virtual {v14}, Landroidx/compose/ui/Modifier$Node;->getChild$ui_release()Landroidx/compose/ui/Modifier$Node;

    move-result-object v14

    const/4 v4, 0x0

    const/4 v13, 0x1

    goto/16 :goto_1

    .line 472
    .end local v26    # "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .end local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .end local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v0    # "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .restart local v3    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    :cond_10
    move-object/from16 v26, v0

    move/from16 v29, v1

    move-object/from16 v30, v3

    .end local v0    # "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .end local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .end local v3    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v26    # "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .restart local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    const/4 v4, 0x0

    goto/16 :goto_0

    .line 520
    .end local v12    # "branch$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v14    # "node$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v26    # "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .end local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .end local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v0    # "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .restart local v3    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    :cond_11
    move-object/from16 v26, v0

    move/from16 v29, v1

    move-object/from16 v30, v3

    .line 451
    .end local v0    # "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .end local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .end local v3    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v7    # "mask$iv$iv":I
    .end local v8    # "$this$visitChildren$iv$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v9    # "$i$f$visitChildren":I
    .end local v10    # "branches$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    .end local v11    # "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .restart local v26    # "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .restart local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    nop

    .line 166
    .end local v5    # "type$iv":I
    .end local v6    # "$i$f$visitChildren-6rFNWt0":I
    .end local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    nop

    .line 164
    .end local v26    # "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .end local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    move-object v0, v2

    .line 167
    .local v0, "children":Landroidx/compose/runtime/collection/MutableVector;
    sget-object v1, Landroidx/compose/ui/focus/FocusableChildrenComparator;->INSTANCE:Landroidx/compose/ui/focus/FocusableChildrenComparator;

    check-cast v1, Ljava/util/Comparator;

    invoke-virtual {v0, v1}, Landroidx/compose/runtime/collection/MutableVector;->sortWith(Ljava/util/Comparator;)V

    .line 168
    move-object v1, v0

    .local v1, "this_$iv":Landroidx/compose/runtime/collection/MutableVector;
    const/4 v2, 0x0

    .line 521
    .local v2, "$i$f$forEachReversed":I
    nop

    .line 522
    invoke-virtual {v1}, Landroidx/compose/runtime/collection/MutableVector;->getSize()I

    move-result v3

    .line 523
    .local v3, "size$iv":I
    if-lez v3, :cond_15

    .line 524
    add-int/lit8 v4, v3, -0x1

    .line 525
    .local v4, "i$iv":I
    invoke-virtual {v1}, Landroidx/compose/runtime/collection/MutableVector;->getContent()[Ljava/lang/Object;

    move-result-object v5

    .line 527
    .local v5, "content$iv":[Ljava/lang/Object;
    :cond_12
    aget-object v6, v5, v4

    check-cast v6, Landroidx/compose/ui/focus/FocusTargetNode;

    .local v6, "it":Landroidx/compose/ui/focus/FocusTargetNode;
    const/4 v7, 0x0

    .line 169
    .local v7, "$i$a$-forEachReversed-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$1":I
    invoke-static {v6}, Landroidx/compose/ui/focus/FocusTraversalKt;->isEligibleForFocusSearch(Landroidx/compose/ui/focus/FocusTargetNode;)Z

    move-result v8

    if-eqz v8, :cond_13

    move-object/from16 v10, p1

    invoke-static {v6, v10}, Landroidx/compose/ui/focus/OneDimensionalFocusSearchKt;->backwardFocusSearch(Landroidx/compose/ui/focus/FocusTargetNode;Lkotlin/jvm/functions/Function1;)Z

    move-result v8

    if-eqz v8, :cond_14

    .line 170
    const/4 v8, 0x1

    return v8

    .line 169
    :cond_13
    move-object/from16 v10, p1

    :cond_14
    const/4 v8, 0x1

    .line 172
    nop

    .line 527
    .end local v6    # "it":Landroidx/compose/ui/focus/FocusTargetNode;
    .end local v7    # "$i$a$-forEachReversed-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$1":I
    nop

    .line 528
    add-int/lit8 v4, v4, -0x1

    .line 529
    if-gez v4, :cond_12

    goto :goto_a

    .line 523
    .end local v4    # "i$iv":I
    .end local v5    # "content$iv":[Ljava/lang/Object;
    :cond_15
    move-object/from16 v10, p1

    .line 531
    :goto_a
    nop

    .line 173
    .end local v1    # "this_$iv":Landroidx/compose/runtime/collection/MutableVector;
    .end local v2    # "$i$f$forEachReversed":I
    .end local v3    # "size$iv":I
    const/4 v1, 0x0

    return v1

    .line 453
    .local v0, "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .local v1, "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .local v3, "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .local v5, "type$iv":I
    .local v6, "$i$f$visitChildren-6rFNWt0":I
    .local v7, "mask$iv$iv":I
    .restart local v8    # "$this$visitChildren$iv$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v9    # "$i$f$visitChildren":I
    :cond_16
    move-object/from16 v26, v0

    move/from16 v29, v1

    .end local v0    # "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .end local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    .restart local v26    # "$this$pickChildForBackwardSearch_u24lambda_u249":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForBackwardSearch$children$1":I
    const/4 v0, 0x0

    .line 452
    .local v0, "$i$a$-check-DelegatableNodeKt$visitChildren$1$iv$iv":I
    nop

    .end local v0    # "$i$a$-check-DelegatableNodeKt$visitChildren$1$iv$iv":I
    new-instance v0, Ljava/lang/IllegalStateException;

    const-string/jumbo v1, "visitChildren called on an unattached node"

    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method private static final pickChildForForwardSearch(Landroidx/compose/ui/focus/FocusTargetNode;Lkotlin/jvm/functions/Function1;)Z
    .locals 32
    .param p0, "$this$pickChildForForwardSearch"    # Landroidx/compose/ui/focus/FocusTargetNode;
    .param p1, "onFound"    # Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/compose/ui/focus/FocusTargetNode;",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroidx/compose/ui/focus/FocusTargetNode;",
            "Ljava/lang/Boolean;",
            ">;)Z"
        }
    .end annotation

    .line 154
    nop

    .line 364
    const/16 v0, 0x10

    .local v0, "capacity$iv":I
    const/4 v1, 0x0

    .line 365
    .local v1, "$i$f$MutableVector":I
    new-instance v2, Landroidx/compose/runtime/collection/MutableVector;

    new-array v3, v0, [Landroidx/compose/ui/focus/FocusTargetNode;

    const/4 v4, 0x0

    invoke-direct {v2, v3, v4}, Landroidx/compose/runtime/collection/MutableVector;-><init>([Ljava/lang/Object;I)V

    .line 154
    .end local v0    # "capacity$iv":I
    .end local v1    # "$i$f$MutableVector":I
    move-object v0, v2

    .local v0, "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    const/4 v1, 0x0

    .line 155
    .local v1, "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    move-object/from16 v3, p0

    check-cast v3, Landroidx/compose/ui/node/DelegatableNode;

    const/4 v5, 0x0

    .line 366
    .local v5, "$i$f$getFocusTarget-OLwlOKw":I
    const/16 v6, 0x400

    invoke-static {v6}, Landroidx/compose/ui/node/NodeKind;->constructor-impl(I)I

    move-result v5

    .line 155
    .end local v5    # "$i$f$getFocusTarget-OLwlOKw":I
    nop

    .local v3, "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .local v5, "type$iv":I
    const/4 v6, 0x0

    .line 367
    .local v6, "$i$f$visitChildren-6rFNWt0":I
    move v7, v5

    .local v7, "mask$iv$iv":I
    move-object v8, v3

    .local v8, "$this$visitChildren$iv$iv":Landroidx/compose/ui/node/DelegatableNode;
    const/4 v9, 0x0

    .line 368
    .local v9, "$i$f$visitChildren":I
    invoke-interface {v8}, Landroidx/compose/ui/node/DelegatableNode;->getNode()Landroidx/compose/ui/Modifier$Node;

    move-result-object v10

    invoke-virtual {v10}, Landroidx/compose/ui/Modifier$Node;->isAttached()Z

    move-result v10

    if-eqz v10, :cond_17

    .line 370
    const/4 v10, 0x0

    .line 371
    .local v10, "$i$f$mutableVectorOf":I
    nop

    .line 372
    const/16 v11, 0x10

    .local v11, "capacity$iv$iv$iv$iv":I
    const/4 v12, 0x0

    .line 373
    .local v12, "$i$f$MutableVector":I
    new-instance v13, Landroidx/compose/runtime/collection/MutableVector;

    new-array v14, v11, [Landroidx/compose/ui/Modifier$Node;

    invoke-direct {v13, v14, v4}, Landroidx/compose/runtime/collection/MutableVector;-><init>([Ljava/lang/Object;I)V

    .line 371
    .end local v11    # "capacity$iv$iv$iv$iv":I
    .end local v12    # "$i$f$MutableVector":I
    nop

    .line 370
    .end local v10    # "$i$f$mutableVectorOf":I
    move-object v10, v13

    .line 374
    .local v10, "branches$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    invoke-interface {v8}, Landroidx/compose/ui/node/DelegatableNode;->getNode()Landroidx/compose/ui/Modifier$Node;

    move-result-object v11

    invoke-virtual {v11}, Landroidx/compose/ui/Modifier$Node;->getChild$ui_release()Landroidx/compose/ui/Modifier$Node;

    move-result-object v11

    .line 375
    .local v11, "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    if-nez v11, :cond_0

    .line 376
    invoke-interface {v8}, Landroidx/compose/ui/node/DelegatableNode;->getNode()Landroidx/compose/ui/Modifier$Node;

    move-result-object v12

    invoke-static {v10, v12}, Landroidx/compose/ui/node/DelegatableNodeKt;->access$addLayoutNodeChildren(Landroidx/compose/runtime/collection/MutableVector;Landroidx/compose/ui/Modifier$Node;)V

    goto :goto_0

    .line 378
    :cond_0
    invoke-virtual {v10, v11}, Landroidx/compose/runtime/collection/MutableVector;->add(Ljava/lang/Object;)Z

    .line 379
    :goto_0
    invoke-virtual {v10}, Landroidx/compose/runtime/collection/MutableVector;->isNotEmpty()Z

    move-result v12

    const/4 v13, 0x1

    if-eqz v12, :cond_11

    .line 380
    move-object v12, v10

    .local v12, "this_$iv$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    const/4 v14, 0x0

    .line 381
    .local v14, "$i$f$getLastIndex":I
    invoke-virtual {v12}, Landroidx/compose/runtime/collection/MutableVector;->getSize()I

    move-result v15

    sub-int/2addr v15, v13

    .line 380
    .end local v12    # "this_$iv$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    .end local v14    # "$i$f$getLastIndex":I
    invoke-virtual {v10, v15}, Landroidx/compose/runtime/collection/MutableVector;->removeAt(I)Ljava/lang/Object;

    move-result-object v12

    check-cast v12, Landroidx/compose/ui/Modifier$Node;

    .line 382
    .local v12, "branch$iv$iv":Landroidx/compose/ui/Modifier$Node;
    invoke-virtual {v12}, Landroidx/compose/ui/Modifier$Node;->getAggregateChildKindSet$ui_release()I

    move-result v14

    and-int/2addr v14, v7

    if-nez v14, :cond_1

    .line 383
    invoke-static {v10, v12}, Landroidx/compose/ui/node/DelegatableNodeKt;->access$addLayoutNodeChildren(Landroidx/compose/runtime/collection/MutableVector;Landroidx/compose/ui/Modifier$Node;)V

    .line 385
    goto :goto_0

    .line 387
    :cond_1
    move-object v14, v12

    .line 388
    .local v14, "node$iv$iv":Landroidx/compose/ui/Modifier$Node;
    :goto_1
    if-eqz v14, :cond_10

    .line 389
    invoke-virtual {v14}, Landroidx/compose/ui/Modifier$Node;->getKindSet$ui_release()I

    move-result v15

    and-int/2addr v15, v7

    if-eqz v15, :cond_f

    .line 390
    move-object v15, v14

    .local v15, "it$iv":Landroidx/compose/ui/Modifier$Node;
    const/16 v16, 0x0

    .line 367
    .local v16, "$i$a$-visitChildren-DelegatableNodeKt$visitChildren$2$iv":I
    move-object/from16 v17, v15

    .local v17, "$this$dispatchForKind_u2d6rFNWt0$iv$iv":Landroidx/compose/ui/Modifier$Node;
    const/16 v18, 0x0

    .line 391
    .local v18, "$i$f$dispatchForKind-6rFNWt0":I
    const/16 v19, 0x0

    .line 392
    .local v19, "stack$iv$iv":Ljava/lang/Object;
    const/16 v20, 0x0

    .local v20, "node$iv$iv":Ljava/lang/Object;
    move-object/from16 v20, v17

    move-object/from16 v4, v20

    .line 393
    .end local v20    # "node$iv$iv":Ljava/lang/Object;
    .local v4, "node$iv$iv":Ljava/lang/Object;
    :goto_2
    if-eqz v4, :cond_e

    .line 394
    instance-of v13, v4, Landroidx/compose/ui/focus/FocusTargetNode;

    if-eqz v13, :cond_2

    .line 395
    move-object v13, v4

    check-cast v13, Landroidx/compose/ui/focus/FocusTargetNode;

    .local v13, "it":Landroidx/compose/ui/focus/FocusTargetNode;
    const/16 v21, 0x0

    .line 155
    .local v21, "$i$a$-visitChildren-6rFNWt0-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1$1":I
    invoke-virtual {v0, v13}, Landroidx/compose/runtime/collection/MutableVector;->add(Ljava/lang/Object;)Z

    .line 395
    .end local v13    # "it":Landroidx/compose/ui/focus/FocusTargetNode;
    .end local v21    # "$i$a$-visitChildren-6rFNWt0-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1$1":I
    move-object/from16 v26, v0

    move/from16 v29, v1

    move-object/from16 v30, v3

    const/4 v0, 0x1

    goto/16 :goto_9

    .line 396
    :cond_2
    move-object v13, v4

    .local v13, "this_$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    const/16 v21, 0x0

    .line 397
    .local v21, "$i$f$isKind-H91voCI$ui_release":I
    invoke-virtual {v13}, Landroidx/compose/ui/Modifier$Node;->getKindSet$ui_release()I

    move-result v22

    and-int v22, v22, v5

    if-eqz v22, :cond_3

    const/4 v13, 0x1

    goto :goto_3

    :cond_3
    const/4 v13, 0x0

    .line 396
    .end local v13    # "this_$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v21    # "$i$f$isKind-H91voCI$ui_release":I
    :goto_3
    if-eqz v13, :cond_c

    instance-of v13, v4, Landroidx/compose/ui/node/DelegatingNode;

    if-eqz v13, :cond_c

    .line 398
    const/4 v13, 0x0

    .line 399
    .local v13, "count$iv$iv":I
    move-object/from16 v21, v4

    check-cast v21, Landroidx/compose/ui/node/DelegatingNode;

    .local v21, "this_$iv$iv$iv":Landroidx/compose/ui/node/DelegatingNode;
    const/16 v22, 0x0

    .line 400
    .local v22, "$i$f$forEachImmediateDelegate$ui_release":I
    invoke-virtual/range {v21 .. v21}, Landroidx/compose/ui/node/DelegatingNode;->getDelegate$ui_release()Landroidx/compose/ui/Modifier$Node;

    move-result-object v23

    .line 401
    .local v23, "node$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    :goto_4
    if-eqz v23, :cond_b

    .line 402
    move-object/from16 v24, v23

    .local v24, "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    const/16 v25, 0x0

    .line 403
    .local v25, "$i$a$-forEachImmediateDelegate$ui_release-DelegatableNodeKt$dispatchForKind$1$iv$iv":I
    move-object/from16 v26, v24

    .local v26, "this_$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    const/16 v27, 0x0

    .line 397
    .local v27, "$i$f$isKind-H91voCI$ui_release":I
    invoke-virtual/range {v26 .. v26}, Landroidx/compose/ui/Modifier$Node;->getKindSet$ui_release()I

    move-result v28

    and-int v28, v28, v5

    if-eqz v28, :cond_4

    const/16 v26, 0x1

    goto :goto_5

    :cond_4
    const/16 v26, 0x0

    .line 403
    .end local v26    # "this_$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v27    # "$i$f$isKind-H91voCI$ui_release":I
    :goto_5
    if-eqz v26, :cond_a

    .line 404
    add-int/lit8 v13, v13, 0x1

    .line 405
    move-object/from16 v26, v0

    const/4 v0, 0x1

    .end local v0    # "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    .local v26, "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    if-ne v13, v0, :cond_5

    .line 406
    move-object/from16 v4, v24

    move/from16 v29, v1

    move-object/from16 v30, v3

    move-object/from16 v0, v24

    goto :goto_8

    .line 410
    :cond_5
    if-nez v19, :cond_6

    const/4 v0, 0x0

    .line 371
    .local v0, "$i$f$mutableVectorOf":I
    nop

    .line 372
    move/from16 v27, v0

    .end local v0    # "$i$f$mutableVectorOf":I
    .local v27, "$i$f$mutableVectorOf":I
    const/16 v0, 0x10

    .local v0, "capacity$iv$iv$iv$iv":I
    const/16 v28, 0x0

    .line 373
    .local v28, "$i$f$MutableVector":I
    move/from16 v29, v1

    .end local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .local v29, "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    new-instance v1, Landroidx/compose/runtime/collection/MutableVector;

    move-object/from16 v30, v3

    .end local v3    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .local v30, "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    new-array v3, v0, [Landroidx/compose/ui/Modifier$Node;

    move/from16 v31, v0

    const/4 v0, 0x0

    .end local v0    # "capacity$iv$iv$iv$iv":I
    .local v31, "capacity$iv$iv$iv$iv":I
    invoke-direct {v1, v3, v0}, Landroidx/compose/runtime/collection/MutableVector;-><init>([Ljava/lang/Object;I)V

    .line 371
    .end local v28    # "$i$f$MutableVector":I
    .end local v31    # "capacity$iv$iv$iv$iv":I
    goto :goto_6

    .line 410
    .end local v27    # "$i$f$mutableVectorOf":I
    .end local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .end local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .restart local v3    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    :cond_6
    move/from16 v29, v1

    move-object/from16 v30, v3

    const/4 v0, 0x0

    .end local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .end local v3    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .restart local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    move-object/from16 v1, v19

    :goto_6
    nop

    .line 411
    .end local v19    # "stack$iv$iv":Ljava/lang/Object;
    .local v1, "stack$iv$iv":Ljava/lang/Object;
    move-object v3, v4

    .line 412
    .local v3, "theNode$iv$iv":Landroidx/compose/ui/Modifier$Node;
    if-eqz v3, :cond_8

    .line 413
    if-eqz v1, :cond_7

    invoke-virtual {v1, v3}, Landroidx/compose/runtime/collection/MutableVector;->add(Ljava/lang/Object;)Z

    .line 414
    :cond_7
    const/4 v4, 0x0

    .line 416
    :cond_8
    if-eqz v1, :cond_9

    move-object/from16 v0, v24

    .end local v24    # "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .local v0, "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    invoke-virtual {v1, v0}, Landroidx/compose/runtime/collection/MutableVector;->add(Ljava/lang/Object;)Z

    goto :goto_7

    .end local v0    # "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .restart local v24    # "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    :cond_9
    move-object/from16 v0, v24

    .line 419
    .end local v3    # "theNode$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v24    # "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .restart local v0    # "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    :goto_7
    move-object/from16 v19, v1

    goto :goto_8

    .line 403
    .end local v26    # "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    .end local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .end local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .local v0, "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    .local v1, "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .local v3, "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v19    # "stack$iv$iv":Ljava/lang/Object;
    .restart local v24    # "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    :cond_a
    move-object/from16 v26, v0

    move/from16 v29, v1

    move-object/from16 v30, v3

    move-object/from16 v0, v24

    .line 419
    .end local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .end local v3    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v24    # "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .local v0, "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .restart local v26    # "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .restart local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    :goto_8
    nop

    .line 402
    .end local v0    # "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v25    # "$i$a$-forEachImmediateDelegate$ui_release-DelegatableNodeKt$dispatchForKind$1$iv$iv":I
    nop

    .line 420
    invoke-virtual/range {v23 .. v23}, Landroidx/compose/ui/Modifier$Node;->getChild$ui_release()Landroidx/compose/ui/Modifier$Node;

    move-result-object v23

    move-object/from16 v0, v26

    move/from16 v1, v29

    move-object/from16 v3, v30

    goto :goto_4

    .line 422
    .end local v26    # "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    .end local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .end local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .local v0, "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .restart local v3    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    :cond_b
    move-object/from16 v26, v0

    move/from16 v29, v1

    move-object/from16 v30, v3

    .line 423
    .end local v0    # "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    .end local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .end local v3    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v21    # "this_$iv$iv$iv":Landroidx/compose/ui/node/DelegatingNode;
    .end local v22    # "$i$f$forEachImmediateDelegate$ui_release":I
    .end local v23    # "node$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .restart local v26    # "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .restart local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    const/4 v0, 0x1

    if-ne v13, v0, :cond_d

    .line 425
    move v13, v0

    move-object/from16 v0, v26

    move/from16 v1, v29

    move-object/from16 v3, v30

    goto/16 :goto_2

    .line 396
    .end local v13    # "count$iv$iv":I
    .end local v26    # "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    .end local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .end local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v0    # "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .restart local v3    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    :cond_c
    move-object/from16 v26, v0

    move/from16 v29, v1

    move-object/from16 v30, v3

    const/4 v0, 0x1

    .line 428
    .end local v0    # "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    .end local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .end local v3    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v26    # "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .restart local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    :cond_d
    :goto_9
    invoke-static/range {v19 .. v19}, Landroidx/compose/ui/node/DelegatableNodeKt;->access$pop(Landroidx/compose/runtime/collection/MutableVector;)Landroidx/compose/ui/Modifier$Node;

    move-result-object v4

    move v13, v0

    move-object/from16 v0, v26

    move/from16 v1, v29

    move-object/from16 v3, v30

    goto/16 :goto_2

    .line 430
    .end local v26    # "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    .end local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .end local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v0    # "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .restart local v3    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    :cond_e
    move-object/from16 v26, v0

    move/from16 v29, v1

    move-object/from16 v30, v3

    .line 367
    .end local v0    # "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    .end local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .end local v3    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v4    # "node$iv$iv":Ljava/lang/Object;
    .end local v17    # "$this$dispatchForKind_u2d6rFNWt0$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v18    # "$i$f$dispatchForKind-6rFNWt0":I
    .end local v19    # "stack$iv$iv":Ljava/lang/Object;
    .restart local v26    # "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .restart local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    nop

    .line 390
    .end local v15    # "it$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v16    # "$i$a$-visitChildren-DelegatableNodeKt$visitChildren$2$iv":I
    nop

    .line 431
    const/4 v4, 0x0

    goto/16 :goto_0

    .line 433
    .end local v26    # "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    .end local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .end local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v0    # "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .restart local v3    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    :cond_f
    move-object/from16 v26, v0

    move/from16 v29, v1

    move-object/from16 v30, v3

    move v0, v13

    .end local v0    # "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    .end local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .end local v3    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v26    # "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .restart local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    invoke-virtual {v14}, Landroidx/compose/ui/Modifier$Node;->getChild$ui_release()Landroidx/compose/ui/Modifier$Node;

    move-result-object v14

    move-object/from16 v0, v26

    const/4 v4, 0x0

    goto/16 :goto_1

    .line 388
    .end local v26    # "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    .end local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .end local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v0    # "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .restart local v3    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    :cond_10
    move-object/from16 v26, v0

    move/from16 v29, v1

    move-object/from16 v30, v3

    .end local v0    # "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    .end local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .end local v3    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v26    # "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .restart local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    const/4 v4, 0x0

    goto/16 :goto_0

    .line 436
    .end local v12    # "branch$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v14    # "node$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v26    # "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    .end local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .end local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v0    # "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .restart local v3    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    :cond_11
    move-object/from16 v26, v0

    move/from16 v29, v1

    move-object/from16 v30, v3

    move v0, v13

    .line 367
    .end local v0    # "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    .end local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .end local v3    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v7    # "mask$iv$iv":I
    .end local v8    # "$this$visitChildren$iv$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v9    # "$i$f$visitChildren":I
    .end local v10    # "branches$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    .end local v11    # "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .restart local v26    # "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .restart local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    nop

    .line 156
    .end local v5    # "type$iv":I
    .end local v6    # "$i$f$visitChildren-6rFNWt0":I
    .end local v30    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    nop

    .line 154
    .end local v26    # "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    .end local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    move-object v1, v2

    .line 157
    .local v1, "children":Landroidx/compose/runtime/collection/MutableVector;
    sget-object v2, Landroidx/compose/ui/focus/FocusableChildrenComparator;->INSTANCE:Landroidx/compose/ui/focus/FocusableChildrenComparator;

    check-cast v2, Ljava/util/Comparator;

    invoke-virtual {v1, v2}, Landroidx/compose/runtime/collection/MutableVector;->sortWith(Ljava/util/Comparator;)V

    .line 158
    move-object v2, v1

    .local v2, "this_$iv":Landroidx/compose/runtime/collection/MutableVector;
    const/4 v3, 0x0

    .line 437
    .local v3, "$i$f$any":I
    nop

    .line 438
    invoke-virtual {v2}, Landroidx/compose/runtime/collection/MutableVector;->getSize()I

    move-result v4

    .line 439
    .local v4, "size$iv":I
    if-lez v4, :cond_16

    .line 440
    const/4 v5, 0x0

    .line 441
    .local v5, "i$iv":I
    invoke-virtual {v2}, Landroidx/compose/runtime/collection/MutableVector;->getContent()[Ljava/lang/Object;

    move-result-object v6

    .line 443
    .local v6, "content$iv":[Ljava/lang/Object;
    :cond_12
    aget-object v7, v6, v5

    check-cast v7, Landroidx/compose/ui/focus/FocusTargetNode;

    .local v7, "it":Landroidx/compose/ui/focus/FocusTargetNode;
    const/4 v8, 0x0

    .line 158
    .local v8, "$i$a$-any-OneDimensionalFocusSearchKt$pickChildForForwardSearch$1":I
    invoke-static {v7}, Landroidx/compose/ui/focus/FocusTraversalKt;->isEligibleForFocusSearch(Landroidx/compose/ui/focus/FocusTargetNode;)Z

    move-result v9

    if-eqz v9, :cond_13

    move-object/from16 v10, p1

    invoke-static {v7, v10}, Landroidx/compose/ui/focus/OneDimensionalFocusSearchKt;->forwardFocusSearch(Landroidx/compose/ui/focus/FocusTargetNode;Lkotlin/jvm/functions/Function1;)Z

    move-result v9

    if-eqz v9, :cond_14

    move v7, v0

    goto :goto_a

    :cond_13
    move-object/from16 v10, p1

    :cond_14
    const/4 v7, 0x0

    .line 443
    .end local v7    # "it":Landroidx/compose/ui/focus/FocusTargetNode;
    .end local v8    # "$i$a$-any-OneDimensionalFocusSearchKt$pickChildForForwardSearch$1":I
    :goto_a
    if-eqz v7, :cond_15

    move v4, v0

    goto :goto_c

    .line 444
    :cond_15
    add-int/lit8 v5, v5, 0x1

    .line 445
    if-lt v5, v4, :cond_12

    goto :goto_b

    .line 439
    .end local v5    # "i$iv":I
    .end local v6    # "content$iv":[Ljava/lang/Object;
    :cond_16
    move-object/from16 v10, p1

    .line 447
    :goto_b
    const/4 v4, 0x0

    .line 158
    .end local v2    # "this_$iv":Landroidx/compose/runtime/collection/MutableVector;
    .end local v3    # "$i$f$any":I
    .end local v4    # "size$iv":I
    :goto_c
    return v4

    .line 369
    .restart local v0    # "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    .local v1, "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .local v3, "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .local v5, "type$iv":I
    .local v6, "$i$f$visitChildren-6rFNWt0":I
    .local v7, "mask$iv$iv":I
    .local v8, "$this$visitChildren$iv$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v9    # "$i$f$visitChildren":I
    :cond_17
    move-object/from16 v26, v0

    move/from16 v29, v1

    .end local v0    # "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    .end local v1    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    .restart local v26    # "$this$pickChildForForwardSearch_u24lambda_u246":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v29    # "$i$a$-apply-OneDimensionalFocusSearchKt$pickChildForForwardSearch$children$1":I
    const/4 v0, 0x0

    .line 368
    .local v0, "$i$a$-check-DelegatableNodeKt$visitChildren$1$iv$iv":I
    nop

    .end local v0    # "$i$a$-check-DelegatableNodeKt$visitChildren$1$iv$iv":I
    new-instance v0, Ljava/lang/IllegalStateException;

    const-string/jumbo v1, "visitChildren called on an unattached node"

    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method private static final searchChildren-4C6V_qg(Landroidx/compose/ui/focus/FocusTargetNode;Landroidx/compose/ui/focus/FocusTargetNode;ILkotlin/jvm/functions/Function1;)Z
    .locals 37
    .param p0, "$this$searchChildren_u2d4C6V_qg"    # Landroidx/compose/ui/focus/FocusTargetNode;
    .param p1, "focusedItem"    # Landroidx/compose/ui/focus/FocusTargetNode;
    .param p2, "direction"    # I
    .param p3, "onFound"    # Lkotlin/jvm/functions/Function1;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroidx/compose/ui/focus/FocusTargetNode;",
            "Landroidx/compose/ui/focus/FocusTargetNode;",
            "I",
            "Lkotlin/jvm/functions/Function1<",
            "-",
            "Landroidx/compose/ui/focus/FocusTargetNode;",
            "Ljava/lang/Boolean;",
            ">;)Z"
        }
    .end annotation

    .line 125
    move-object/from16 v0, p0

    move-object/from16 v1, p1

    move/from16 v2, p2

    move-object/from16 v3, p3

    invoke-virtual/range {p0 .. p0}, Landroidx/compose/ui/focus/FocusTargetNode;->getFocusState()Landroidx/compose/ui/focus/FocusStateImpl;

    move-result-object v4

    sget-object v5, Landroidx/compose/ui/focus/FocusStateImpl;->ActiveParent:Landroidx/compose/ui/focus/FocusStateImpl;

    const/4 v6, 0x0

    if-ne v4, v5, :cond_0

    const/4 v4, 0x1

    goto :goto_0

    :cond_0
    move v4, v6

    :goto_0
    if-eqz v4, :cond_20

    .line 128
    nop

    .line 267
    const/16 v4, 0x10

    .local v4, "capacity$iv":I
    const/4 v5, 0x0

    .line 268
    .local v5, "$i$f$MutableVector":I
    new-instance v8, Landroidx/compose/runtime/collection/MutableVector;

    new-array v9, v4, [Landroidx/compose/ui/focus/FocusTargetNode;

    invoke-direct {v8, v9, v6}, Landroidx/compose/runtime/collection/MutableVector;-><init>([Ljava/lang/Object;I)V

    .line 128
    .end local v4    # "capacity$iv":I
    .end local v5    # "$i$f$MutableVector":I
    move-object v4, v8

    .local v4, "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    const/4 v5, 0x0

    .line 129
    .local v5, "$i$a$-apply-OneDimensionalFocusSearchKt$searchChildren$children$1":I
    move-object v9, v0

    check-cast v9, Landroidx/compose/ui/node/DelegatableNode;

    const/4 v10, 0x0

    .line 269
    .local v10, "$i$f$getFocusTarget-OLwlOKw":I
    const/16 v11, 0x400

    invoke-static {v11}, Landroidx/compose/ui/node/NodeKind;->constructor-impl(I)I

    move-result v10

    .line 129
    .end local v10    # "$i$f$getFocusTarget-OLwlOKw":I
    nop

    .local v9, "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .local v10, "type$iv":I
    const/4 v11, 0x0

    .line 270
    .local v11, "$i$f$visitChildren-6rFNWt0":I
    move v12, v10

    .local v12, "mask$iv$iv":I
    move-object v13, v9

    .local v13, "$this$visitChildren$iv$iv":Landroidx/compose/ui/node/DelegatableNode;
    const/4 v14, 0x0

    .line 271
    .local v14, "$i$f$visitChildren":I
    invoke-interface {v13}, Landroidx/compose/ui/node/DelegatableNode;->getNode()Landroidx/compose/ui/Modifier$Node;

    move-result-object v15

    invoke-virtual {v15}, Landroidx/compose/ui/Modifier$Node;->isAttached()Z

    move-result v15

    if-eqz v15, :cond_1f

    .line 273
    const/4 v15, 0x0

    .line 274
    .local v15, "$i$f$mutableVectorOf":I
    nop

    .line 275
    const/16 v7, 0x10

    .local v7, "capacity$iv$iv$iv$iv":I
    const/16 v17, 0x0

    .line 276
    .local v17, "$i$f$MutableVector":I
    move/from16 v18, v5

    .end local v5    # "$i$a$-apply-OneDimensionalFocusSearchKt$searchChildren$children$1":I
    .local v18, "$i$a$-apply-OneDimensionalFocusSearchKt$searchChildren$children$1":I
    new-instance v5, Landroidx/compose/runtime/collection/MutableVector;

    move-object/from16 v19, v9

    .end local v9    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .local v19, "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    new-array v9, v7, [Landroidx/compose/ui/Modifier$Node;

    invoke-direct {v5, v9, v6}, Landroidx/compose/runtime/collection/MutableVector;-><init>([Ljava/lang/Object;I)V

    .line 274
    .end local v7    # "capacity$iv$iv$iv$iv":I
    .end local v17    # "$i$f$MutableVector":I
    nop

    .line 273
    .end local v15    # "$i$f$mutableVectorOf":I
    nop

    .line 277
    .local v5, "branches$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    invoke-interface {v13}, Landroidx/compose/ui/node/DelegatableNode;->getNode()Landroidx/compose/ui/Modifier$Node;

    move-result-object v7

    invoke-virtual {v7}, Landroidx/compose/ui/Modifier$Node;->getChild$ui_release()Landroidx/compose/ui/Modifier$Node;

    move-result-object v7

    .line 278
    .local v7, "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    if-nez v7, :cond_1

    .line 279
    invoke-interface {v13}, Landroidx/compose/ui/node/DelegatableNode;->getNode()Landroidx/compose/ui/Modifier$Node;

    move-result-object v9

    invoke-static {v5, v9}, Landroidx/compose/ui/node/DelegatableNodeKt;->access$addLayoutNodeChildren(Landroidx/compose/runtime/collection/MutableVector;Landroidx/compose/ui/Modifier$Node;)V

    goto :goto_1

    .line 281
    :cond_1
    invoke-virtual {v5, v7}, Landroidx/compose/runtime/collection/MutableVector;->add(Ljava/lang/Object;)Z

    .line 282
    :goto_1
    invoke-virtual {v5}, Landroidx/compose/runtime/collection/MutableVector;->isNotEmpty()Z

    move-result v9

    if-eqz v9, :cond_12

    .line 283
    move-object v9, v5

    .local v9, "this_$iv$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    const/4 v15, 0x0

    .line 284
    .local v15, "$i$f$getLastIndex":I
    invoke-virtual {v9}, Landroidx/compose/runtime/collection/MutableVector;->getSize()I

    move-result v17

    const/16 v16, 0x1

    add-int/lit8 v9, v17, -0x1

    .line 283
    .end local v9    # "this_$iv$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    .end local v15    # "$i$f$getLastIndex":I
    invoke-virtual {v5, v9}, Landroidx/compose/runtime/collection/MutableVector;->removeAt(I)Ljava/lang/Object;

    move-result-object v9

    check-cast v9, Landroidx/compose/ui/Modifier$Node;

    .line 285
    .local v9, "branch$iv$iv":Landroidx/compose/ui/Modifier$Node;
    invoke-virtual {v9}, Landroidx/compose/ui/Modifier$Node;->getAggregateChildKindSet$ui_release()I

    move-result v15

    and-int/2addr v15, v12

    if-nez v15, :cond_2

    .line 286
    invoke-static {v5, v9}, Landroidx/compose/ui/node/DelegatableNodeKt;->access$addLayoutNodeChildren(Landroidx/compose/runtime/collection/MutableVector;Landroidx/compose/ui/Modifier$Node;)V

    .line 288
    goto :goto_1

    .line 290
    :cond_2
    move-object v15, v9

    .line 291
    .local v15, "node$iv$iv":Landroidx/compose/ui/Modifier$Node;
    :goto_2
    if-eqz v15, :cond_11

    .line 292
    invoke-virtual {v15}, Landroidx/compose/ui/Modifier$Node;->getKindSet$ui_release()I

    move-result v17

    and-int v17, v17, v12

    if-eqz v17, :cond_10

    .line 293
    move-object/from16 v17, v15

    .local v17, "it$iv":Landroidx/compose/ui/Modifier$Node;
    const/16 v20, 0x0

    .line 270
    .local v20, "$i$a$-visitChildren-DelegatableNodeKt$visitChildren$2$iv":I
    move-object/from16 v21, v17

    .local v21, "$this$dispatchForKind_u2d6rFNWt0$iv$iv":Landroidx/compose/ui/Modifier$Node;
    const/16 v22, 0x0

    .line 294
    .local v22, "$i$f$dispatchForKind-6rFNWt0":I
    const/16 v23, 0x0

    .line 295
    .local v23, "stack$iv$iv":Ljava/lang/Object;
    const/16 v24, 0x0

    .local v24, "node$iv$iv":Ljava/lang/Object;
    move-object/from16 v24, v21

    move-object/from16 v6, v24

    .line 296
    .end local v24    # "node$iv$iv":Ljava/lang/Object;
    .local v6, "node$iv$iv":Ljava/lang/Object;
    :goto_3
    if-eqz v6, :cond_f

    .line 297
    move-object/from16 v25, v5

    .end local v5    # "branches$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    .local v25, "branches$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    instance-of v5, v6, Landroidx/compose/ui/focus/FocusTargetNode;

    if-eqz v5, :cond_3

    .line 298
    move-object v5, v6

    check-cast v5, Landroidx/compose/ui/focus/FocusTargetNode;

    .local v5, "it":Landroidx/compose/ui/focus/FocusTargetNode;
    const/16 v26, 0x0

    .line 129
    .local v26, "$i$a$-visitChildren-6rFNWt0-OneDimensionalFocusSearchKt$searchChildren$children$1$1":I
    invoke-virtual {v4, v5}, Landroidx/compose/runtime/collection/MutableVector;->add(Ljava/lang/Object;)Z

    .line 298
    .end local v5    # "it":Landroidx/compose/ui/focus/FocusTargetNode;
    .end local v26    # "$i$a$-visitChildren-6rFNWt0-OneDimensionalFocusSearchKt$searchChildren$children$1$1":I
    move-object/from16 v31, v4

    move-object/from16 v35, v7

    goto/16 :goto_a

    .line 299
    :cond_3
    move-object v5, v6

    .local v5, "this_$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    const/16 v26, 0x0

    .line 300
    .local v26, "$i$f$isKind-H91voCI$ui_release":I
    invoke-virtual {v5}, Landroidx/compose/ui/Modifier$Node;->getKindSet$ui_release()I

    move-result v27

    and-int v27, v27, v10

    if-eqz v27, :cond_4

    const/4 v5, 0x1

    goto :goto_4

    :cond_4
    const/4 v5, 0x0

    .line 299
    .end local v5    # "this_$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v26    # "$i$f$isKind-H91voCI$ui_release":I
    :goto_4
    if-eqz v5, :cond_d

    instance-of v5, v6, Landroidx/compose/ui/node/DelegatingNode;

    if-eqz v5, :cond_d

    .line 301
    const/4 v5, 0x0

    .line 302
    .local v5, "count$iv$iv":I
    move-object/from16 v26, v6

    check-cast v26, Landroidx/compose/ui/node/DelegatingNode;

    .local v26, "this_$iv$iv$iv":Landroidx/compose/ui/node/DelegatingNode;
    const/16 v27, 0x0

    .line 303
    .local v27, "$i$f$forEachImmediateDelegate$ui_release":I
    invoke-virtual/range {v26 .. v26}, Landroidx/compose/ui/node/DelegatingNode;->getDelegate$ui_release()Landroidx/compose/ui/Modifier$Node;

    move-result-object v28

    .line 304
    .local v28, "node$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    :goto_5
    if-eqz v28, :cond_c

    .line 305
    move-object/from16 v29, v28

    .local v29, "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    const/16 v30, 0x0

    .line 306
    .local v30, "$i$a$-forEachImmediateDelegate$ui_release-DelegatableNodeKt$dispatchForKind$1$iv$iv":I
    move-object/from16 v31, v29

    .local v31, "this_$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    const/16 v32, 0x0

    .line 300
    .local v32, "$i$f$isKind-H91voCI$ui_release":I
    invoke-virtual/range {v31 .. v31}, Landroidx/compose/ui/Modifier$Node;->getKindSet$ui_release()I

    move-result v33

    and-int v33, v33, v10

    if-eqz v33, :cond_5

    const/16 v31, 0x1

    goto :goto_6

    :cond_5
    const/16 v31, 0x0

    .line 306
    .end local v31    # "this_$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v32    # "$i$f$isKind-H91voCI$ui_release":I
    :goto_6
    if-eqz v31, :cond_b

    .line 307
    add-int/lit8 v5, v5, 0x1

    .line 308
    move-object/from16 v31, v4

    const/4 v4, 0x1

    .end local v4    # "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    .local v31, "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    if-ne v5, v4, :cond_6

    .line 309
    move-object/from16 v6, v29

    move-object/from16 v35, v7

    move-object/from16 v7, v29

    goto :goto_9

    .line 313
    :cond_6
    if-nez v23, :cond_7

    const/4 v4, 0x0

    .line 274
    .local v4, "$i$f$mutableVectorOf":I
    nop

    .line 275
    move/from16 v32, v4

    .end local v4    # "$i$f$mutableVectorOf":I
    .local v32, "$i$f$mutableVectorOf":I
    const/16 v4, 0x10

    .local v4, "capacity$iv$iv$iv$iv":I
    const/16 v33, 0x0

    .line 276
    .local v33, "$i$f$MutableVector":I
    move/from16 v34, v5

    .end local v5    # "count$iv$iv":I
    .local v34, "count$iv$iv":I
    new-instance v5, Landroidx/compose/runtime/collection/MutableVector;

    move-object/from16 v35, v7

    .end local v7    # "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .local v35, "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    new-array v7, v4, [Landroidx/compose/ui/Modifier$Node;

    move/from16 v36, v4

    const/4 v4, 0x0

    .end local v4    # "capacity$iv$iv$iv$iv":I
    .local v36, "capacity$iv$iv$iv$iv":I
    invoke-direct {v5, v7, v4}, Landroidx/compose/runtime/collection/MutableVector;-><init>([Ljava/lang/Object;I)V

    .line 274
    .end local v33    # "$i$f$MutableVector":I
    .end local v36    # "capacity$iv$iv$iv$iv":I
    goto :goto_7

    .line 313
    .end local v32    # "$i$f$mutableVectorOf":I
    .end local v34    # "count$iv$iv":I
    .end local v35    # "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .restart local v5    # "count$iv$iv":I
    .restart local v7    # "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    :cond_7
    move/from16 v34, v5

    move-object/from16 v35, v7

    .end local v5    # "count$iv$iv":I
    .end local v7    # "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .restart local v34    # "count$iv$iv":I
    .restart local v35    # "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    move-object/from16 v5, v23

    :goto_7
    move-object v4, v5

    .line 314
    .end local v23    # "stack$iv$iv":Ljava/lang/Object;
    .local v4, "stack$iv$iv":Ljava/lang/Object;
    move-object v5, v6

    .line 315
    .local v5, "theNode$iv$iv":Landroidx/compose/ui/Modifier$Node;
    if-eqz v5, :cond_9

    .line 316
    if-eqz v4, :cond_8

    invoke-virtual {v4, v5}, Landroidx/compose/runtime/collection/MutableVector;->add(Ljava/lang/Object;)Z

    .line 317
    :cond_8
    const/4 v6, 0x0

    .line 319
    :cond_9
    if-eqz v4, :cond_a

    move-object/from16 v7, v29

    .end local v29    # "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .local v7, "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    invoke-virtual {v4, v7}, Landroidx/compose/runtime/collection/MutableVector;->add(Ljava/lang/Object;)Z

    goto :goto_8

    .end local v7    # "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .restart local v29    # "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    :cond_a
    move-object/from16 v7, v29

    .line 322
    .end local v5    # "theNode$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v29    # "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .restart local v7    # "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    :goto_8
    move-object/from16 v23, v4

    move/from16 v5, v34

    goto :goto_9

    .line 306
    .end local v31    # "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    .end local v34    # "count$iv$iv":I
    .end local v35    # "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .local v4, "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    .local v5, "count$iv$iv":I
    .local v7, "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .restart local v23    # "stack$iv$iv":Ljava/lang/Object;
    .restart local v29    # "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    :cond_b
    move-object/from16 v31, v4

    move-object/from16 v35, v7

    move-object/from16 v7, v29

    .line 322
    .end local v4    # "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    .end local v29    # "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .local v7, "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .restart local v31    # "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v35    # "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    :goto_9
    nop

    .line 305
    .end local v7    # "next$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v30    # "$i$a$-forEachImmediateDelegate$ui_release-DelegatableNodeKt$dispatchForKind$1$iv$iv":I
    nop

    .line 323
    invoke-virtual/range {v28 .. v28}, Landroidx/compose/ui/Modifier$Node;->getChild$ui_release()Landroidx/compose/ui/Modifier$Node;

    move-result-object v28

    move-object/from16 v4, v31

    move-object/from16 v7, v35

    goto :goto_5

    .line 325
    .end local v31    # "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    .end local v35    # "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .restart local v4    # "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    .local v7, "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    :cond_c
    move-object/from16 v31, v4

    move-object/from16 v35, v7

    .line 326
    .end local v4    # "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    .end local v7    # "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v26    # "this_$iv$iv$iv":Landroidx/compose/ui/node/DelegatingNode;
    .end local v27    # "$i$f$forEachImmediateDelegate$ui_release":I
    .end local v28    # "node$iv$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .restart local v31    # "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v35    # "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    const/4 v4, 0x1

    if-ne v5, v4, :cond_e

    .line 328
    move-object/from16 v5, v25

    move-object/from16 v4, v31

    move-object/from16 v7, v35

    goto/16 :goto_3

    .line 299
    .end local v5    # "count$iv$iv":I
    .end local v31    # "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    .end local v35    # "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .restart local v4    # "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v7    # "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    :cond_d
    move-object/from16 v31, v4

    move-object/from16 v35, v7

    .line 331
    .end local v4    # "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    .end local v7    # "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .restart local v31    # "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v35    # "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    :cond_e
    :goto_a
    invoke-static/range {v23 .. v23}, Landroidx/compose/ui/node/DelegatableNodeKt;->access$pop(Landroidx/compose/runtime/collection/MutableVector;)Landroidx/compose/ui/Modifier$Node;

    move-result-object v6

    move-object/from16 v5, v25

    move-object/from16 v4, v31

    move-object/from16 v7, v35

    goto/16 :goto_3

    .line 333
    .end local v25    # "branches$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    .end local v31    # "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    .end local v35    # "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .restart local v4    # "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    .local v5, "branches$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v7    # "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    :cond_f
    move-object/from16 v31, v4

    move-object/from16 v25, v5

    move-object/from16 v35, v7

    .line 270
    .end local v4    # "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    .end local v5    # "branches$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    .end local v6    # "node$iv$iv":Ljava/lang/Object;
    .end local v7    # "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v21    # "$this$dispatchForKind_u2d6rFNWt0$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v22    # "$i$f$dispatchForKind-6rFNWt0":I
    .end local v23    # "stack$iv$iv":Ljava/lang/Object;
    .restart local v25    # "branches$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v31    # "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v35    # "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    nop

    .line 293
    .end local v17    # "it$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v20    # "$i$a$-visitChildren-DelegatableNodeKt$visitChildren$2$iv":I
    nop

    .line 334
    const/4 v6, 0x0

    goto/16 :goto_1

    .line 336
    .end local v25    # "branches$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    .end local v31    # "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    .end local v35    # "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .restart local v4    # "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v5    # "branches$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v7    # "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    :cond_10
    move-object/from16 v31, v4

    move-object/from16 v25, v5

    move-object/from16 v35, v7

    .end local v4    # "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    .end local v5    # "branches$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    .end local v7    # "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .restart local v25    # "branches$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v31    # "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v35    # "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    invoke-virtual {v15}, Landroidx/compose/ui/Modifier$Node;->getChild$ui_release()Landroidx/compose/ui/Modifier$Node;

    move-result-object v15

    const/4 v6, 0x0

    goto/16 :goto_2

    .line 291
    .end local v25    # "branches$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    .end local v31    # "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    .end local v35    # "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .restart local v4    # "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v5    # "branches$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v7    # "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    :cond_11
    move-object/from16 v31, v4

    move-object/from16 v25, v5

    move-object/from16 v35, v7

    .end local v4    # "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    .end local v5    # "branches$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    .end local v7    # "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .restart local v25    # "branches$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v31    # "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v35    # "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    const/4 v6, 0x0

    goto/16 :goto_1

    .line 339
    .end local v9    # "branch$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v15    # "node$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v25    # "branches$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    .end local v31    # "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    .end local v35    # "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .restart local v4    # "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v5    # "branches$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    .restart local v7    # "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    :cond_12
    move-object/from16 v31, v4

    move-object/from16 v25, v5

    move-object/from16 v35, v7

    .line 270
    .end local v4    # "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    .end local v5    # "branches$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    .end local v7    # "child$iv$iv":Landroidx/compose/ui/Modifier$Node;
    .end local v12    # "mask$iv$iv":I
    .end local v13    # "$this$visitChildren$iv$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v14    # "$i$f$visitChildren":I
    .restart local v31    # "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    nop

    .line 130
    .end local v10    # "type$iv":I
    .end local v11    # "$i$f$visitChildren-6rFNWt0":I
    .end local v19    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    nop

    .line 128
    .end local v18    # "$i$a$-apply-OneDimensionalFocusSearchKt$searchChildren$children$1":I
    .end local v31    # "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    move-object v4, v8

    .line 131
    .local v4, "children":Landroidx/compose/runtime/collection/MutableVector;
    sget-object v5, Landroidx/compose/ui/focus/FocusableChildrenComparator;->INSTANCE:Landroidx/compose/ui/focus/FocusableChildrenComparator;

    check-cast v5, Ljava/util/Comparator;

    invoke-virtual {v4, v5}, Landroidx/compose/runtime/collection/MutableVector;->sortWith(Ljava/util/Comparator;)V

    .line 132
    nop

    .line 133
    sget-object v5, Landroidx/compose/ui/focus/FocusDirection;->Companion:Landroidx/compose/ui/focus/FocusDirection$Companion;

    invoke-virtual {v5}, Landroidx/compose/ui/focus/FocusDirection$Companion;->getNext-dhqQ-8s()I

    move-result v5

    invoke-static {v2, v5}, Landroidx/compose/ui/focus/FocusDirection;->equals-impl0(II)Z

    move-result v5

    if-eqz v5, :cond_17

    move-object v5, v4

    .local v5, "$this$forEachItemAfter$iv":Landroidx/compose/runtime/collection/MutableVector;
    const/4 v6, 0x0

    .line 340
    .local v6, "$i$f$forEachItemAfter":I
    nop

    .line 341
    const/4 v7, 0x0

    .line 342
    .local v7, "itemFound$iv":Z
    move-object v8, v5

    .local v8, "this_$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    const/4 v9, 0x0

    .line 343
    .local v9, "$i$f$getIndices":I
    new-instance v10, Lkotlin/ranges/IntRange;

    invoke-virtual {v8}, Landroidx/compose/runtime/collection/MutableVector;->getSize()I

    move-result v11

    const/4 v12, 0x1

    sub-int/2addr v11, v12

    const/4 v12, 0x0

    invoke-direct {v10, v12, v11}, Lkotlin/ranges/IntRange;-><init>(II)V

    .line 342
    .end local v8    # "this_$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    .end local v9    # "$i$f$getIndices":I
    invoke-virtual {v10}, Lkotlin/ranges/IntRange;->getFirst()I

    move-result v8

    .local v8, "index$iv":I
    invoke-virtual {v10}, Lkotlin/ranges/IntRange;->getLast()I

    move-result v9

    if-gt v8, v9, :cond_16

    .line 344
    :goto_b
    if-eqz v7, :cond_14

    .line 345
    move-object v10, v5

    .local v10, "this_$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    const/4 v11, 0x0

    .line 346
    .local v11, "$i$f$get":I
    invoke-virtual {v10}, Landroidx/compose/runtime/collection/MutableVector;->getContent()[Ljava/lang/Object;

    move-result-object v12

    aget-object v10, v12, v8

    .line 345
    .end local v10    # "this_$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    .end local v11    # "$i$f$get":I
    check-cast v10, Landroidx/compose/ui/focus/FocusTargetNode;

    .local v10, "child":Landroidx/compose/ui/focus/FocusTargetNode;
    const/4 v11, 0x0

    .line 134
    .local v11, "$i$a$-forEachItemAfter-OneDimensionalFocusSearchKt$searchChildren$2":I
    invoke-static {v10}, Landroidx/compose/ui/focus/FocusTraversalKt;->isEligibleForFocusSearch(Landroidx/compose/ui/focus/FocusTargetNode;)Z

    move-result v12

    if-eqz v12, :cond_13

    invoke-static {v10, v3}, Landroidx/compose/ui/focus/OneDimensionalFocusSearchKt;->forwardFocusSearch(Landroidx/compose/ui/focus/FocusTargetNode;Lkotlin/jvm/functions/Function1;)Z

    move-result v12

    if-eqz v12, :cond_13

    const/4 v9, 0x1

    return v9

    .line 135
    :cond_13
    nop

    .line 345
    .end local v10    # "child":Landroidx/compose/ui/focus/FocusTargetNode;
    .end local v11    # "$i$a$-forEachItemAfter-OneDimensionalFocusSearchKt$searchChildren$2":I
    nop

    .line 347
    :cond_14
    move-object v10, v5

    .local v10, "this_$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    const/4 v11, 0x0

    .line 346
    .local v11, "$i$f$get":I
    invoke-virtual {v10}, Landroidx/compose/runtime/collection/MutableVector;->getContent()[Ljava/lang/Object;

    move-result-object v12

    aget-object v10, v12, v8

    .line 347
    .end local v10    # "this_$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    .end local v11    # "$i$f$get":I
    invoke-static {v10, v1}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v10

    if-eqz v10, :cond_15

    .line 348
    const/4 v7, 0x1

    .line 342
    :cond_15
    if-eq v8, v9, :cond_16

    add-int/lit8 v8, v8, 0x1

    goto :goto_b

    .line 351
    .end local v8    # "index$iv":I
    :cond_16
    nop

    .end local v5    # "$this$forEachItemAfter$iv":Landroidx/compose/runtime/collection/MutableVector;
    .end local v6    # "$i$f$forEachItemAfter":I
    .end local v7    # "itemFound$iv":Z
    goto :goto_e

    .line 136
    :cond_17
    sget-object v5, Landroidx/compose/ui/focus/FocusDirection;->Companion:Landroidx/compose/ui/focus/FocusDirection$Companion;

    invoke-virtual {v5}, Landroidx/compose/ui/focus/FocusDirection$Companion;->getPrevious-dhqQ-8s()I

    move-result v5

    invoke-static {v2, v5}, Landroidx/compose/ui/focus/FocusDirection;->equals-impl0(II)Z

    move-result v5

    if-eqz v5, :cond_1e

    move-object v5, v4

    .local v5, "$this$forEachItemBefore$iv":Landroidx/compose/runtime/collection/MutableVector;
    const/4 v6, 0x0

    .line 352
    .local v6, "$i$f$forEachItemBefore":I
    nop

    .line 353
    const/4 v7, 0x0

    .line 354
    .restart local v7    # "itemFound$iv":Z
    move-object v8, v5

    .local v8, "this_$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    const/4 v9, 0x0

    .line 355
    .restart local v9    # "$i$f$getIndices":I
    new-instance v10, Lkotlin/ranges/IntRange;

    invoke-virtual {v8}, Landroidx/compose/runtime/collection/MutableVector;->getSize()I

    move-result v11

    const/4 v12, 0x1

    sub-int/2addr v11, v12

    const/4 v12, 0x0

    invoke-direct {v10, v12, v11}, Lkotlin/ranges/IntRange;-><init>(II)V

    .line 354
    .end local v8    # "this_$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    .end local v9    # "$i$f$getIndices":I
    invoke-virtual {v10}, Lkotlin/ranges/IntRange;->getFirst()I

    move-result v8

    invoke-virtual {v10}, Lkotlin/ranges/IntRange;->getLast()I

    move-result v9

    .local v9, "index$iv":I
    if-gt v8, v9, :cond_1b

    .line 356
    :goto_c
    if-eqz v7, :cond_19

    .line 357
    move-object v10, v5

    .restart local v10    # "this_$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    const/4 v11, 0x0

    .line 358
    .restart local v11    # "$i$f$get":I
    invoke-virtual {v10}, Landroidx/compose/runtime/collection/MutableVector;->getContent()[Ljava/lang/Object;

    move-result-object v12

    aget-object v10, v12, v9

    .line 357
    .end local v10    # "this_$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    .end local v11    # "$i$f$get":I
    check-cast v10, Landroidx/compose/ui/focus/FocusTargetNode;

    .local v10, "child":Landroidx/compose/ui/focus/FocusTargetNode;
    const/4 v11, 0x0

    .line 137
    .local v11, "$i$a$-forEachItemBefore-OneDimensionalFocusSearchKt$searchChildren$3":I
    invoke-static {v10}, Landroidx/compose/ui/focus/FocusTraversalKt;->isEligibleForFocusSearch(Landroidx/compose/ui/focus/FocusTargetNode;)Z

    move-result v12

    if-eqz v12, :cond_18

    invoke-static {v10, v3}, Landroidx/compose/ui/focus/OneDimensionalFocusSearchKt;->backwardFocusSearch(Landroidx/compose/ui/focus/FocusTargetNode;Lkotlin/jvm/functions/Function1;)Z

    move-result v12

    if-eqz v12, :cond_18

    const/4 v12, 0x1

    return v12

    :cond_18
    const/4 v12, 0x1

    .line 138
    nop

    .line 357
    .end local v10    # "child":Landroidx/compose/ui/focus/FocusTargetNode;
    .end local v11    # "$i$a$-forEachItemBefore-OneDimensionalFocusSearchKt$searchChildren$3":I
    goto :goto_d

    .line 356
    :cond_19
    const/4 v12, 0x1

    .line 359
    :goto_d
    move-object v10, v5

    .local v10, "this_$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    const/4 v11, 0x0

    .line 358
    .local v11, "$i$f$get":I
    invoke-virtual {v10}, Landroidx/compose/runtime/collection/MutableVector;->getContent()[Ljava/lang/Object;

    move-result-object v13

    aget-object v10, v13, v9

    .line 359
    .end local v10    # "this_$iv$iv":Landroidx/compose/runtime/collection/MutableVector;
    .end local v11    # "$i$f$get":I
    invoke-static {v10, v1}, Lkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v10

    if-eqz v10, :cond_1a

    .line 360
    const/4 v7, 0x1

    .line 354
    :cond_1a
    if-eq v9, v8, :cond_1b

    add-int/lit8 v9, v9, -0x1

    goto :goto_c

    .line 363
    .end local v9    # "index$iv":I
    :cond_1b
    nop

    .line 146
    .end local v5    # "$this$forEachItemBefore$iv":Landroidx/compose/runtime/collection/MutableVector;
    .end local v6    # "$i$f$forEachItemBefore":I
    .end local v7    # "itemFound$iv":Z
    :goto_e
    sget-object v5, Landroidx/compose/ui/focus/FocusDirection;->Companion:Landroidx/compose/ui/focus/FocusDirection$Companion;

    invoke-virtual {v5}, Landroidx/compose/ui/focus/FocusDirection$Companion;->getNext-dhqQ-8s()I

    move-result v5

    invoke-static {v2, v5}, Landroidx/compose/ui/focus/FocusDirection;->equals-impl0(II)Z

    move-result v5

    if-nez v5, :cond_1d

    invoke-virtual/range {p0 .. p0}, Landroidx/compose/ui/focus/FocusTargetNode;->fetchFocusProperties$ui_release()Landroidx/compose/ui/focus/FocusProperties;

    move-result-object v5

    invoke-interface {v5}, Landroidx/compose/ui/focus/FocusProperties;->getCanFocus()Z

    move-result v5

    if-eqz v5, :cond_1d

    invoke-static/range {p0 .. p0}, Landroidx/compose/ui/focus/OneDimensionalFocusSearchKt;->isRoot(Landroidx/compose/ui/focus/FocusTargetNode;)Z

    move-result v5

    if-eqz v5, :cond_1c

    goto :goto_f

    .line 148
    :cond_1c
    invoke-interface {v3, v0}, Lkotlin/jvm/functions/Function1;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Ljava/lang/Boolean;

    invoke-virtual {v5}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v5

    return v5

    .line 146
    :cond_1d
    :goto_f
    const/4 v5, 0x0

    return v5

    .line 363
    :cond_1e
    new-instance v5, Ljava/lang/IllegalStateException;

    .line 139
    const-string v6, "This function should only be used for 1-D focus search"

    invoke-virtual {v6}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v6

    invoke-direct {v5, v6}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v5

    .line 272
    .local v4, "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    .local v5, "$i$a$-apply-OneDimensionalFocusSearchKt$searchChildren$children$1":I
    .local v9, "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .local v10, "type$iv":I
    .local v11, "$i$f$visitChildren-6rFNWt0":I
    .restart local v12    # "mask$iv$iv":I
    .restart local v13    # "$this$visitChildren$iv$iv":Landroidx/compose/ui/node/DelegatableNode;
    .restart local v14    # "$i$f$visitChildren":I
    :cond_1f
    move-object/from16 v31, v4

    move/from16 v18, v5

    .end local v4    # "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    .end local v5    # "$i$a$-apply-OneDimensionalFocusSearchKt$searchChildren$children$1":I
    .restart local v18    # "$i$a$-apply-OneDimensionalFocusSearchKt$searchChildren$children$1":I
    .restart local v31    # "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    const/4 v4, 0x0

    .line 271
    .local v4, "$i$a$-check-DelegatableNodeKt$visitChildren$1$iv$iv":I
    nop

    .end local v4    # "$i$a$-check-DelegatableNodeKt$visitChildren$1$iv$iv":I
    new-instance v4, Ljava/lang/IllegalStateException;

    const-string/jumbo v5, "visitChildren called on an unattached node"

    invoke-virtual {v5}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-direct {v4, v5}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v4

    .line 125
    .end local v9    # "$this$visitChildren_u2d6rFNWt0$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v10    # "type$iv":I
    .end local v11    # "$i$f$visitChildren-6rFNWt0":I
    .end local v12    # "mask$iv$iv":I
    .end local v13    # "$this$visitChildren$iv$iv":Landroidx/compose/ui/node/DelegatableNode;
    .end local v14    # "$i$f$visitChildren":I
    .end local v18    # "$i$a$-apply-OneDimensionalFocusSearchKt$searchChildren$children$1":I
    .end local v31    # "$this$searchChildren_4C6V_qg_u24lambda_u242":Landroidx/compose/runtime/collection/MutableVector;
    :cond_20
    const/4 v4, 0x0

    .line 126
    .local v4, "$i$a$-check-OneDimensionalFocusSearchKt$searchChildren$1":I
    nop

    .line 125
    .end local v4    # "$i$a$-check-OneDimensionalFocusSearchKt$searchChildren$1":I
    new-instance v4, Ljava/lang/IllegalStateException;

    const-string v5, "This function should only be used within a parent that has focus."

    invoke-virtual {v5}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-direct {v4, v5}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v4
.end method
