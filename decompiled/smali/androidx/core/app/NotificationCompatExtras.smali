.class public final Landroidx/core/app/NotificationCompatExtras;
.super Ljava/lang/Object;
.source "NotificationCompatExtras.java"


# static fields
.field public static final EXTRA_ACTION_EXTRAS:Ljava/lang/String; = "android.support.actionExtras"

.field public static final EXTRA_GROUP_KEY:Ljava/lang/String; = "android.support.groupKey"

.field public static final EXTRA_GROUP_SUMMARY:Ljava/lang/String; = "android.support.isGroupSummary"

.field public static final EXTRA_LOCAL_ONLY:Ljava/lang/String; = "android.support.localOnly"

.field public static final EXTRA_REMOTE_INPUTS:Ljava/lang/String; = "android.support.remoteInputs"

.field public static final EXTRA_SORT_KEY:Ljava/lang/String; = "android.support.sortKey"


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 66
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
